# Golang-ADB

[![go doc](https://godoc.org/github.com/electricbubble/gadb?status.svg)](https://pkg.go.dev/github.com/electricbubble/gadb?tab=doc#pkg-index)
[![go report](https://goreportcard.com/badge/github.com/electricbubble/gadb)](https://goreportcard.com/report/github.com/electricbubble/gadb)
[![license](https://img.shields.io/github/license/electricbubble/gadb)](https://github.com/electricbubble/gadb/blob/master/LICENSE)

## Installation
```shell script
go get github.com/electricbubble/gadb
```

## Example
```go
package main

import (
	"log"
	"os"
	"strings"

	"github.com/electricbubble/gadb"
)

func main() {
	adbClient, err := gadb.NewClient()
	checkErr(err, "fail to connect adb server")

	devices, err := adbClient.ListDevices()
	checkErr(err)

	if len(devices) == 0 {
		log.Fatalln("list of devices is empty")
	}

	dev := devices[0]

	userHomeDir, _ := os.UserHomeDir()
	apk, err := os.Open(userHomeDir + "zhibo8.apk")
	checkErr(err)

	log.Println("starting to push apk")

	remotePath := "/data/local/tmp/zhibo8.apk"
	err = dev.PushFile(apk, remotePath)
	checkErr(err, "adb push")

	log.Println("push completed")

	log.Println("starting to install apk")

	shellOutput, err := dev.RunShellCommand("pm install", remotePath)
	checkErr(err, "pm install")
	if !strings.Contains(shellOutput, "Success") {
		log.Fatalln("fail to install: ", shellOutput)
	}

	log.Println("install completed")

}

func checkErr(err error, msg ...string) {
	if err == nil {
		return
	}

	var output string
	if len(msg) != 0 {
		output = msg[0] + " "
	}
	output += err.Error()
	log.Fatalln(output)
}

```

## Thanks

Thank you [JetBrains](https://www.jetbrains.com/?from=gwda) for providing free open source licenses

---

Repository|Description
---|---
[zach-klippenstein/goadb](https://github.com/zach-klippenstein/goadb)|A Golang library for interacting with adb.
[vidstige/jadb](https://github.com/vidstige/jadb)|ADB Client in pure Java.
[Swind/pure-python-adb](https://github.com/Swind/pure-python-adb)|This is pure-python implementation of the ADB client.
[codeskyblue/fa](https://github.com/codeskyblue/fa)|FA(fast adb) helps you win at ADB(Android Debug Bridge).
