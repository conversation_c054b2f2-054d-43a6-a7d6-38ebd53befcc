package gadb

import (
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"
)

var deviceStateStrings = map[string]DeviceState{
	"":        DeviceStateOfDisconnected,
	"offline": DeviceStateOfOffline,
	"device":  DeviceStateOfOnline,
}

func deviceStateConv(k string) (deviceState DeviceState) {
	var ok bool
	if deviceState, ok = deviceStateStrings[k]; !ok {
		return DeviceStateOfUnknown
	}
	return
}

type (
	Device struct {
		client *Client

		serial string
		info   DeviceInfo
	}

	DeviceForward struct {
		Serial string
		Local  string
		Remote string
	}

	DeviceOption  func(o *deviceOptions)
	deviceOptions struct {
		daemonPort int

		fileModTime time.Time
		fileMode    os.FileMode
	}
)

func WithDaemonPort(port int) DeviceOption {
	return func(o *deviceOptions) {
		o.daemonPort = port
	}
}

func WithFileModTime(modTime time.Time) DeviceOption {
	return func(o *deviceOptions) {
		o.fileModTime = modTime
	}
}

func WithFileMode(mode os.FileMode) DeviceOption {
	return func(o *deviceOptions) {
		o.fileMode = mode
	}
}

func (d *Device) Product() string {
	return d.info.Product
}

func (d *Device) Model() string {
	return d.info.Model
}

func (d *Device) USB() string {
	return d.info.USB
}

func (d *Device) transportID() string {
	return d.info.TransportID
}

func (d *Device) DeviceInfo() DeviceInfo {
	return d.info
}

func (d *Device) Serial() string {
	// 	resp, err := d.client.executeCommand(fmt.Sprintf("host-serial:%s:get-serialno", d.serial))
	return d.serial
}

func (d *Device) IsUSB() bool {
	return d.USB() != ""
}

func (d *Device) State() (DeviceState, error) {
	resp, err := d.client.executeCommand(fmt.Sprintf(hostServiceOfGetStateBySerial, d.serial), false)
	return deviceStateConv(resp), err
}

func (d *Device) DevicePath() (string, error) {
	resp, err := d.client.executeCommand(fmt.Sprintf(hostServiceOfGetDevPathBySerial, d.serial), false)
	return resp, err
}

func (d *Device) Forward(localPort, remotePort int, noRebind ...bool) error {
	command := ""
	local := fmt.Sprintf("tcp:%d", localPort)
	remote := fmt.Sprintf("tcp:%d", remotePort)

	if len(noRebind) != 0 && noRebind[0] {
		command = fmt.Sprintf(hostServiceOfForwardNoRebindBySerial, d.serial, local, remote)
	} else {
		command = fmt.Sprintf(hostServiceOfForwardBySerial, d.serial, local, remote)
	}

	_, err := d.client.executeCommand(command, true)
	return err
}

func (d *Device) ForwardList() ([]DeviceForward, error) {
	forwardList, err := d.client.ForwardList()
	if err != nil {
		return nil, err
	}

	deviceForwardList := make([]DeviceForward, 0, len(forwardList))
	for i := range forwardList {
		if forwardList[i].Serial == d.serial {
			deviceForwardList = append(deviceForwardList, forwardList[i])
		}
	}

	return deviceForwardList, nil
}

func (d *Device) ForwardKill(localPort int) error {
	local := fmt.Sprintf("tcp:%d", localPort)
	_, err := d.client.executeCommand(fmt.Sprintf(hostServiceOfKillForwardBySerial, d.serial, local), true)
	return err
}

func (d *Device) RunShellCommand(cmd string, args ...string) (string, error) {
	raw, err := d.RunShellCommandWithBytes(cmd, args...)
	return string(raw), err
}

func (d *Device) RunShellCommandWithBytes(cmd string, args ...string) ([]byte, error) {
	if len(args) > 0 {
		cmd = fmt.Sprintf("%s %s", cmd, strings.Join(args, " "))
	}
	if strings.TrimSpace(cmd) == "" {
		return nil, errors.New("adb shell: command cannot be empty")
	}
	raw, err := d.executeCommand(fmt.Sprintf(localServiceOfShell, cmd), false)
	return raw, err
}

func (d *Device) EnableADBOverTCP(opts ...DeviceOption) error {
	o := &deviceOptions{
		daemonPort: ADBDaemonPort,
	}
	for _, opt := range opts {
		opt(o)
	}

	raw, err := d.executeCommand(fmt.Sprintf(localServiceOfTCPIP, o.daemonPort), true)
	debugLogf("adb over tcp: %s", raw)
	return err
}

func (d *Device) CreateDeviceTransport() (*Transport, error) {
	tp, err := newTransport(d.client.address)
	if err != nil {
		return nil, err
	}

	if err = tp.Send(fmt.Sprintf(hostServiceOfTransportBySerial, d.serial)); err != nil {
		return nil, err
	}

	err = tp.VerifyResponse()
	return tp, err
}

func (d *Device) executeCommand(command string, onlyVerifyResponse bool) ([]byte, error) {
	tp, err := d.CreateDeviceTransport()
	if err != nil {
		return nil, err
	}
	defer func() { _ = tp.Close() }()

	if err = tp.Send(command); err != nil {
		return nil, err
	}

	if err = tp.VerifyResponse(); err != nil {
		return nil, err
	}

	if onlyVerifyResponse {
		return []byte{}, nil
	}

	return tp.ReadBytesAll()
}

func (d *Device) Exists(remotePath string) bool {
	fileInfo, err := d.Stat(remotePath)
	if err != nil {
		return false
	}

	return !fileInfo.modTime.IsZero()
}

func (d *Device) Stat(remotePath string) (*FileInfo, error) {
	tp, err := d.CreateDeviceTransport()
	if err != nil {
		return nil, err
	}
	defer func() { _ = tp.Close() }()

	sync, err := tp.CreateSyncTransport()
	if err != nil {
		return nil, err
	}
	defer func() { _ = sync.Close() }()

	return sync.Stat(remotePath)
}

func (d *Device) List(remotePath string) ([]*FileInfo, error) {
	tp, err := d.CreateDeviceTransport()
	if err != nil {
		return nil, err
	}
	defer func() { _ = tp.Close() }()

	sync, err := tp.CreateSyncTransport()
	if err != nil {
		return nil, err
	}
	defer func() { _ = sync.Close() }()

	return sync.List(remotePath)
}

func (d *Device) Push(src, dst string, opts ...DeviceOption) error {
	info, err := os.Stat(src)
	if err != nil {
		return fmt.Errorf("failed to stat %q: %w", src, err)
	}

	if !info.IsDir() {
		return d.PushDir(src, dst, opts...)
	}

	return d.PushFile(src, dst, opts...)
}

func (d *Device) PushFile(localFile, remotePath string, opts ...DeviceOption) error {
	info, err := os.Stat(localFile)
	if err != nil {
		return fmt.Errorf("failed to stat %q: %w", localFile, err)
	} else if info.IsDir() {
		return fmt.Errorf("%q is not a file", localFile)
	}

	tp, err := d.CreateDeviceTransport()
	if err != nil {
		return err
	}
	defer func() { _ = tp.Close() }()

	sync, err := tp.CreateSyncTransport()
	if err != nil {
		return err
	}
	defer func() { _ = sync.Close() }()

	return d.pushFile(sync, localFile, remotePath, opts...)
}

func (d *Device) PushByReader(source io.Reader, remotePath string, opts ...DeviceOption) error {
	tp, err := d.CreateDeviceTransport()
	if err != nil {
		return err
	}
	defer func() { _ = tp.Close() }()

	sync, err := tp.CreateSyncTransport()
	if err != nil {
		return err
	}
	defer func() { _ = sync.Close() }()

	info, err := sync.Stat(remotePath)
	if err == nil && info.IsDir() {
		return fmt.Errorf("%q is a directory", remotePath)
	}

	return d.pushByReader(sync, source, remotePath, opts...)
}

// PushDir pushes a local directory to a remote path on the device.
// This is equivalent to the command: adb push localDir/ remotePath/
func (d *Device) PushDir(localDir, remotePath string, opts ...DeviceOption) error {
	info, err := os.Stat(localDir)
	if err != nil {
		return fmt.Errorf("failed to stat %q: %w", localDir, err)
	} else if !info.IsDir() {
		return fmt.Errorf("%q is not a directory", localDir)
	}

	tp, err := d.CreateDeviceTransport()
	if err != nil {
		return err
	}
	defer func() { _ = tp.Close() }()

	sync, err := tp.CreateSyncTransport()
	if err != nil {
		return err
	}
	defer func() { _ = sync.Close() }()

	return d.pushDir(sync, localDir, remotePath, opts...)
}

func (d *Device) pushDir(sync *SyncTransport, localDir, remotePath string, opts ...DeviceOption) error {
	// Use a stack to traverse directories iteratively
	stack := make([]*pushDirEntry, 0, 64)

	// Add the root directory to the stack
	stack = append(
		stack, &pushDirEntry{
			localPath:  localDir,
			remotePath: remotePath,
			isDir:      true,
		},
	)

	for len(stack) > 0 {
		// Pop from stack
		entry := stack[len(stack)-1]
		stack = stack[:len(stack)-1]

		if entry.isDir {
			// Create remote directory first
			if err := d.createRemoteDir(sync, entry.remotePath); err != nil {
				return fmt.Errorf("failed to create remote directory %q: %w", entry.remotePath, err)
			}

			// Read local directory contents
			entries, err := os.ReadDir(entry.localPath)
			if err != nil {
				return fmt.Errorf("failed to read local directory %q: %w", entry.localPath, err)
			}

			// Add entries to stack (in reverse order to maintain order)
			for i := len(entries) - 1; i >= 0; i-- {
				entryInfo := entries[i]
				stack = append(
					stack, &pushDirEntry{
						localPath:  filepath.Join(entry.localPath, entryInfo.Name()),
						remotePath: filepath.Join(entry.remotePath, entryInfo.Name()),
						isDir:      entryInfo.IsDir(),
					},
				)
			}
		} else {
			// Push file
			if err := d.pushFile(sync, entry.localPath, entry.remotePath, opts...); err != nil {
				return fmt.Errorf("failed to push file %q to %q: %w", entry.localPath, entry.remotePath, err)
			}
		}
	}

	return nil
}

func (d *Device) pushFile(sync *SyncTransport, localFile, remotePath string, opts ...DeviceOption) error {
	file, err := os.Open(localFile)
	if err != nil {
		return fmt.Errorf("failed to open local file %q: %w", localFile, err)
	}
	defer func() { _ = file.Close() }()

	info, err := sync.Stat(remotePath)
	if err == nil && info.IsDir() {
		remotePath = filepath.Join(remotePath, filepath.Base(localFile))
	}

	o := &deviceOptions{
		fileModTime: time.Time{},
	}
	for _, opt := range opts {
		opt(o)
	}

	if o.fileModTime.IsZero() {
		// Get file info for modification time
		info, err := file.Stat()
		if err != nil {
			return fmt.Errorf("failed to stat local file %q: %w", localFile, err)
		}

		// Add file modification time to options if not already set
		opts = append(opts, WithFileModTime(info.ModTime()))
	}

	return d.pushByReader(sync, file, remotePath, opts...)
}

func (d *Device) pushByReader(sync *SyncTransport, source io.Reader, remotePath string, opts ...DeviceOption) error {
	o := &deviceOptions{
		fileModTime: time.Time{},
		fileMode:    DefaultFileMode,
	}
	for _, opt := range opts {
		opt(o)
	}

	if o.fileModTime.IsZero() {
		o.fileModTime = time.Now()
	}

	data := fmt.Sprintf("%s,%d", remotePath, o.fileMode)
	if err := sync.Send(SEND, data); err != nil {
		return err
	}

	if err := sync.SendStream(source); err != nil {
		return err
	}

	if err := sync.SendDone(uint32(o.fileModTime.Unix())); err != nil {
		return err
	}

	if err := sync.VerifyStatus(); err != nil {
		return err
	}

	return nil
}

func (d *Device) createRemoteDir(sync *SyncTransport, remotePath string) error {
	// Check if directory already exists
	fileInfo, err := sync.Stat(remotePath)
	if err == nil && fileInfo.IsDir() {
		// Directory already exists
		return nil
	}

	// Try to create directory using shell command
	// We need to use the device's shell command since sync protocol doesn't support mkdir
	_, err = d.RunShellCommand("mkdir", "-p", remotePath)
	if err != nil {
		return fmt.Errorf("failed to create directory %q: %w", remotePath, err)
	}

	return nil
}

func (d *Device) read(sync *SyncTransport, remotePath string, writer io.Writer) error {
	if sync == nil {
		return errors.New("sync transport is null")
	}

	if err := sync.Send(RECV, remotePath); err != nil {
		return err
	}

	if err := sync.WriteStream(writer); err != nil {
		return err
	}

	return nil
}

func (d *Device) Pull(remotePath, localPath string) error {
	tp, err := d.CreateDeviceTransport()
	if err != nil {
		return err
	}
	defer func() {
		if tp != nil {
			_ = tp.Close()
		}
	}()

	sync, err := tp.CreateSyncTransport()
	if err != nil {
		return err
	}
	defer func() {
		if sync != nil {
			_ = sync.Close()
		}
	}()

	fi, err := sync.Stat(remotePath)
	if err != nil {
		return err
	}

	if fi.IsFile() {
		return d.pullFile(sync, fi, localPath)
	}

	return d.pullDir(sync, fi, localPath)
}

func (d *Device) pullFile(sync *SyncTransport, fi *FileInfo, dst string) error {
	if !fi.IsFile() {
		return fmt.Errorf("%q is not a file", fi.Path())
	}

	file, err := os.OpenFile(dst, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, DefaultFileMode)
	if err != nil {
		return err
	}
	defer func() {
		_ = file.Close()
	}()

	return d.read(sync, fi.Path(), file)
}

type dirEntry struct {
	fi  *FileInfo
	dst string
}

type pushDirEntry struct {
	localPath  string
	remotePath string
	isDir      bool
}

func (d *Device) pullDir(sync *SyncTransport, fi *FileInfo, dst string) error {
	if !fi.IsDir() {
		return fmt.Errorf("%q is not a directory", fi.Path())
	}

	var err error
	defer func() {
		if err != nil {
			_ = os.RemoveAll(dst)
		}
	}()

	stack := make([]*dirEntry, 0, 64)
	stack = append(stack, &dirEntry{fi: fi, dst: dst}) // push

	for len(stack) > 0 {
		// pop
		entry := stack[len(stack)-1]
		stack = stack[:len(stack)-1]

		if err = os.MkdirAll(entry.dst, DefaultDirMode); err != nil {
			return err
		}

		items, err := sync.List(entry.fi.Path())
		if err != nil {
			return err
		}

		for i := len(items) - 1; i >= 0; i-- {
			item := items[i]
			if item.IsDir() {
				stack = append(stack, &dirEntry{fi: item, dst: filepath.Join(entry.dst, item.Name())}) // push
				continue
			}

			if err = d.pullFile(sync, item, filepath.Join(entry.dst, item.Name())); err != nil {
				return err
			}
		}
	}

	return nil
}

func (d *Device) ReadFile(remoteFile string, writer io.Writer) error {
	tp, err := d.CreateDeviceTransport()
	if err != nil {
		return err
	}
	defer func() {
		if tp != nil {
			_ = tp.Close()
		}
	}()

	sync, err := tp.CreateSyncTransport()
	if err != nil {
		return err
	}
	defer func() {
		if sync != nil {
			_ = sync.Close()
		}
	}()

	fi, err := sync.Stat(remoteFile)
	if err != nil {
		return err
	}

	if !fi.IsFile() {
		return fmt.Errorf("%q is not a file", fi.Path())
	}

	return d.read(sync, fi.Path(), writer)
}
