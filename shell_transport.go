package gadb

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"
	"net"
	"slices"
	"time"
)

var validShellMessageTypes = []ShellMessageType{
	shellMessageTypeOfStdin,
	shellMessageTypeOfStdout,
	shellMessageTypeOfStderr,
	shellMessageTypeOfExit,
	shellMessageTypeOfCloseStdin,
}

type ShellTransport struct {
	conn        net.Conn
	readTimeout time.Duration
}

func newShellTransport(conn net.Conn, readTimeout time.Duration) *ShellTransport {
	return &ShellTransport{conn: conn, readTimeout: readTimeout}
}

// Send a packet over the shell protocol.
func (t *ShellTransport) Send(command ShellMessageType, data []byte) error {
	if !slices.Contains(validShellMessageTypes, command) {
		return fmt.Errorf("invalid shell message type: %d", command)
	}

	msg := new(bytes.Buffer)
	if err := msg.WriteByte(byte(command)); err != nil {
		return fmt.Errorf("shell transport write: %w", err)
	}

	if err := binary.Write(msg, binary.LittleEndian, uint32(len(data))); err != nil {
		return fmt.Errorf("shell transport write: %w", err)
	}

	if _, err := msg.Write(data); err != nil {
		return fmt.Errorf("shell transport write: %w", err)
	}

	debugLogf("--> %v", msg.Bytes())
	return _send(t.conn, msg.Bytes())
}

func (t *ShellTransport) Read() (ShellMessageType, []byte, error) {
	var messageType ShellMessageType
	if err := binary.Read(t.conn, binary.LittleEndian, &messageType); err != nil {
		if err == io.EOF {
			return shellMessageTypeOfInvalid, nil, err
		}

		return shellMessageTypeOfInvalid, nil, fmt.Errorf("failed to read response msg type: %w", err)
	}

	var msgLen uint32
	if err := binary.Read(t.conn, binary.LittleEndian, &msgLen); err != nil {
		return messageType, nil, fmt.Errorf("failed to read response msg len: %w", err)
	}

	data, err := t.ReadBytesN(int(msgLen))
	if err != nil {
		return messageType, data, fmt.Errorf("failed to read response msg body: %w", err)
	}

	return messageType, data, nil
}

func (t *ShellTransport) ReadBytesN(size int) ([]byte, error) {
	_ = t.conn.SetReadDeadline(time.Time{})
	return _readN(t.conn, size)
}

func (t *ShellTransport) Close() error {
	if t.conn == nil {
		return nil
	}
	return t.conn.Close()
}
