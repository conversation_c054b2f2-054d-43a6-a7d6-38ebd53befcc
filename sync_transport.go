package gadb

import (
	"bytes"
	"encoding/binary"
	"errors"
	"fmt"
	"io"
	"net"
	"path/filepath"
	"slices"
	"time"
)

var (
	validSyncCommands = []protocol{LIST, RECV, SEND, STAT}

	errSyncReadChunkDone = errors.New("sync read chunk done")
)

type SyncTransport struct {
	conn        net.Conn
	readTimeout time.Duration
}

func newSyncTransport(conn net.Conn, readTimeout time.Duration) *SyncTransport {
	return &SyncTransport{conn: conn, readTimeout: readTimeout}
}

func (t *SyncTransport) Send(command protocol, data string) error {
	if !slices.Contains(validSyncCommands, command) {
		return fmt.Errorf("invalid sync command: %s", command)
	}

	msg := bytes.NewBufferString(string(command))
	if err := binary.Write(msg, binary.LittleEndian, uint32(len(data))); err != nil {
		return fmt.<PERSON><PERSON><PERSON>("sync transport write: %w", err)
	}
	msg.WriteString(data)

	debugLogf("--> %s %s", command, data)
	return _send(t.conn, msg.Bytes())
}

func (t *SyncTransport) SendStream(reader io.Reader) error {
	var (
		n   int
		err error
	)

	for err == nil {
		tmp := make([]byte, syncMaxChunkSize)
		n, err = reader.Read(tmp)
		if err == io.EOF {
			err = nil
			break
		}
		if err == nil {
			err = t.sendChunk(tmp[:n])
		}
	}

	return err
}

func (t *SyncTransport) SendDone(timestamp uint32) error {
	command := DONE
	msg := bytes.NewBufferString(string(command))
	if err := binary.Write(msg, binary.LittleEndian, timestamp); err != nil {
		return fmt.Errorf("sync transport write: %w", err)
	}

	debugLogf("--> %s", command)
	return _send(t.conn, msg.Bytes())
}

func (t *SyncTransport) sendChunk(buffer []byte) error {
	command := DATA
	msg := bytes.NewBufferString(string(command))
	if err := binary.Write(msg, binary.LittleEndian, uint32(len(buffer))); err != nil {
		return fmt.Errorf("sync transport write: %w", err)
	}
	msg.Write(buffer)

	debugLogf("--> %s %s ......", command, buffer)
	return _send(t.conn, msg.Bytes())
}

func (t *SyncTransport) sendCommandWithLength(command protocol, length uint32) error {
	msg := bytes.NewBufferString(string(command))
	if err := binary.Write(msg, binary.LittleEndian, length); err != nil {
		return fmt.Errorf("sync transport write: %w", err)
	}

	debugLogf("--> %s", command)
	return _send(t.conn, msg.Bytes())
}

func (t *SyncTransport) sendCommandWithData(command protocol, data string) error {
	msg := bytes.NewBufferString(string(command))
	if err := binary.Write(msg, binary.LittleEndian, uint32(len(data))); err != nil {
		return fmt.Errorf("sync transport write: %w", err)
	}
	msg.WriteString(data)

	debugLogf("--> %s %s", command, data)
	return _send(t.conn, msg.Bytes())
}

func (t *SyncTransport) VerifyStatus() error {
	status, err := t.ReadStringN(4)
	if err != nil {
		return err
	}

	log := bytes.NewBufferString(fmt.Sprintf("<-- %s", status))
	defer func() {
		debugLog(log.String())
	}()

	length, err := t.ReadUint32()
	if err != nil {
		return fmt.Errorf("verify status (length): %w", err)
	}
	log.WriteString(fmt.Sprintf(" %s\t%d\t", status, length))

	switch status {
	case string(OKAY):
		return nil
	case string(FAIL):
		message, err := t.ReadStringN(int(length))
		if err != nil {
			return fmt.Errorf("verify status (error message): %w", err)
		}
		log.WriteString(message)
		return fmt.Errorf("verify status (fail): %s", message)
	default:
		return fmt.Errorf("verify status (unknown): expected %s or %s, but got %s", OKAY, FAIL, status)
	}
}

func (t *SyncTransport) WriteStream(dest io.Writer) error {
	var (
		chunk []byte
		err   error
	)

	save := func() error {
		chunk, err = t.readChunk()
		if err != nil {
			if errors.Is(err, errSyncReadChunkDone) {
				return err
			}

			return fmt.Errorf("sync read chunk: %w", err)
		}

		if err = _send(dest, chunk); err != nil {
			return fmt.Errorf("sync write stream: %w", err)
		}
		return nil
	}

	for err == nil {
		err = save()
	}

	if errors.Is(err, errSyncReadChunkDone) {
		err = nil
	}
	return err
}

func (t *SyncTransport) readChunk() ([]byte, error) {
	status, err := t.ReadStringN(4)
	if err != nil {
		return nil, err
	}

	log := bytes.NewBufferString("")
	defer func() { debugLog(log.String()) }()

	length, err := t.ReadUint32()
	if err != nil {
		return nil, fmt.Errorf("read chunk (length): %w", err)
	}
	log.WriteString(fmt.Sprintf("<-- %s\t%d\t", status, length))

	switch status {
	case string(DATA):
		chunk, err := t.ReadBytesN(int(length))
		if err != nil {
			return nil, fmt.Errorf("read chunk (data): %w", err)
		}

		log.WriteString("......")
		return chunk, nil
	case string(DONE):
		return nil, errSyncReadChunkDone
	case string(FAIL):
		message, err := t.ReadStringN(int(length))
		if err != nil {
			return nil, fmt.Errorf("read chunk (error message): %w", err)
		}
		log.WriteString(message)

		return nil, fmt.Errorf("read chunk (fail): %s", message)
	default:
		return nil, fmt.Errorf("read chunk (unknown): expected %s, %s or %s, but got %s", DATA, DONE, FAIL, status)
	}
}

func (t *SyncTransport) readError() (string, error) {
	length, err := t.ReadUint32()
	if err != nil {
		return "", fmt.Errorf("read error (length): %w", err)
	}

	message, err := t.ReadStringN(int(length))
	if err != nil {
		return "", fmt.Errorf("read error (message): %w", err)
	}

	return message, nil
}

func (t *SyncTransport) Stat(path string) (*FileInfo, error) {
	if err := t.Send(STAT, path); err != nil {
		return nil, err
	}

	log := bytes.NewBufferString("")
	defer func() {
		debugLog(log.String())
	}()

	status, err := t.ReadStringN(4)
	if err != nil {
		return nil, err
	}

	switch status {
	case string(STAT):
		fileInfo := &FileInfo{name: path}
		if err = binary.Read(t.conn, binary.LittleEndian, &fileInfo.mode); err != nil {
			return nil, fmt.Errorf("stat (mode): %w", err)
		}

		if fileInfo.size, err = t.ReadUint32(); err != nil {
			return nil, fmt.Errorf("stat (size): %w", err)
		}

		modifiedAt, err := t.ReadUint32()
		if err != nil {
			return nil, fmt.Errorf("stat (time): %w", err)
		}
		if modifiedAt == 0 {
			fileInfo.modTime = time.Time{}
		} else {
			fileInfo.modTime = time.Unix(int64(modifiedAt), 0)
		}
		log.WriteString(
			fmt.Sprintf(
				"<-- %s\t%s\t%10d\t%s\n",
				status, fileInfo.Mode(), fileInfo.Size(), fileInfo.ModTime().Format("2006-01-02 15:04:05"),
			),
		)

		return fileInfo, nil
	case string(FAIL):
		length, err := t.ReadUint32()
		if err != nil {
			return nil, fmt.Errorf("stat (length): %w", err)
		}

		message, err := t.ReadStringN(int(length))
		if err != nil {
			return nil, fmt.Errorf("stat (error message): %w", err)
		}
		log.WriteString(fmt.Sprintf("<-- %s\t%d\t%s\n", status, length, message))

		return nil, fmt.Errorf("stat (fail): %s", message)
	default:
		return nil, fmt.Errorf("stat (unknown): expected %s or %s, but got %s", STAT, FAIL, status)
	}
}

func (t *SyncTransport) List(path string) ([]*FileInfo, error) {
	if err := t.Send(LIST, path); err != nil {
		return nil, err
	}

	log := bytes.NewBufferString("")
	defer func() {
		debugLog(log.String())
	}()

	devFileInfos := make([]*FileInfo, 0, 64)
	for {
		status, err := t.ReadStringN(4)
		if err != nil {
			return nil, err
		}

		switch status {
		case string(DENT):
			fileInfo := &FileInfo{}
			if err = binary.Read(t.conn, binary.LittleEndian, &fileInfo.mode); err != nil {
				return nil, fmt.Errorf("list (mode): %w", err)
			}

			if fileInfo.size, err = t.ReadUint32(); err != nil {
				return nil, fmt.Errorf("sync transport read (size): %w", err)
			}

			modifiedAt, err := t.ReadUint32()
			if err != nil {
				return nil, fmt.Errorf("list (time): %w", err)
			}
			fileInfo.modTime = time.Unix(int64(modifiedAt), 0)

			filenameLen, err := t.ReadUint32()
			if err != nil {
				return nil, fmt.Errorf("list (name length): %w", err)
			}

			name, err := t.ReadStringN(int(filenameLen))
			if err != nil {
				return nil, fmt.Errorf("list (name): %w", err)
			}
			if name == "." || name == ".." {
				continue
			}
			fileInfo.name = filepath.Join(path, name)

			log.WriteString(
				fmt.Sprintf(
					"<-- %s\t%s\t%s\t%10d\t%s\n",
					status, fileInfo.Path(), fileInfo.Mode(), fileInfo.Size(),
					fileInfo.ModTime().Format("2006-01-02 15:04:05"),
				),
			)

			devFileInfos = append(devFileInfos, fileInfo)
		case string(DONE):
			message, err := t.ReadBytesN(16)
			if err != nil {
				return nil, fmt.Errorf("list (done message): %w", err)
			}
			log.WriteString(fmt.Sprintf("<-- %s\t%s\n", status, message))

			return devFileInfos, nil
		case string(FAIL):
			length, err := t.ReadUint32()
			if err != nil {
				return nil, fmt.Errorf("list (length): %w", err)
			}

			message, err := t.ReadStringN(int(length))
			if err != nil {
				return nil, fmt.Errorf("list (error message): %w", err)
			}
			log.WriteString(fmt.Sprintf("<-- %s\t%d\t%s\n", status, length, message))

			return nil, fmt.Errorf("list (fail): %s", message)
		default:
			return nil, fmt.Errorf("list (unknown): expected %s, %s or %s, but got %s", DENT, DONE, FAIL, status)
		}
	}
}

func (t *SyncTransport) ReadUint32() (uint32, error) {
	var n uint32
	err := binary.Read(t.conn, binary.LittleEndian, &n)
	return n, err
}

func (t *SyncTransport) ReadStringN(size int) (string, error) {
	raw, err := t.ReadBytesN(size)
	return string(raw), err
}

func (t *SyncTransport) ReadBytesN(size int) ([]byte, error) {
	_ = t.conn.SetReadDeadline(time.Now().Add(t.readTimeout))
	return _readN(t.conn, size)
}

func (t *SyncTransport) Close() error {
	if t.conn == nil {
		return nil
	}
	return t.conn.Close()
}
