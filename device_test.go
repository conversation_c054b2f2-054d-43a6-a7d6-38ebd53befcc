package gadb

import (
	"bytes"
	"encoding/json"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

func TestMain(m *testing.M) {
	SetDebug(true)
	m.Run()
}

func TestDevice_State(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	for i := range devices {
		dev := devices[i]
		state, err := dev.State()
		if err != nil {
			t.Fatal(err)
		}
		t.Log(dev.Serial(), state)
	}
}

func TestDevice_DevicePath(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	for i := range devices {
		dev := devices[i]
		devPath, err := dev.DevicePath()
		if err != nil {
			t.Fatal(err)
		}
		t.Log(dev.Serial(), devPath)
	}
}

func TestDevice_Product(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	for i := range devices {
		dev := devices[i]
		product := dev.Product()
		t.Log(dev.Serial(), product)
	}
}

func TestDevice_Model(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	for i := range devices {
		dev := devices[i]
		t.Log(dev.Serial(), dev.Model())
	}
}

func TestDevice_Usb(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	for i := range devices {
		dev := devices[i]
		t.Log(dev.Serial(), dev.USB(), dev.IsUSB())
	}
}

func TestDevice_DeviceInfo(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	for i := range devices {
		dev := devices[i]
		t.Log(dev.DeviceInfo())
	}
}

func TestDevice_Forward(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	localPort := 61000
	err = devices[0].Forward(localPort, 6790)
	if err != nil {
		t.Fatal(err)
	}

	err = devices[0].ForwardKill(localPort)
	if err != nil {
		t.Fatal(err)
	}
}

func TestDevice_ForwardList(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	for i := range devices {
		dev := devices[i]
		forwardList, err := dev.ForwardList()
		if err != nil {
			t.Fatal(err)
		}
		t.Log(dev.serial, "->", forwardList)
	}
}

func TestDevice_ForwardKill(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	err = devices[0].ForwardKill(6790)
	if err != nil {
		t.Fatal(err)
	}
}

func TestDevice_RunShellCommand(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	// for i := range devices {
	// 	dev := devices[i]
	// 	// cmdOutput, err := dev.RunShellCommand(`pm list packages  | grep  "bili"`)
	// 	// cmdOutput, err := dev.RunShellCommand(`pm list packages`, `| grep "bili"`)
	// 	// cmdOutput, err := dev.RunShellCommand("dumpsys activity | grep mFocusedActivity")
	// 	cmdOutput, err := dev.RunShellCommand("monkey", "-p", "tv.danmaku.bili", "-c", "android.intent.category.LAUNCHER", "1")
	// 	if err != nil {
	// 		t.Fatal(dev.serial, err)
	// 	}
	// 	t.Log("\n"+dev.serial, cmdOutput)
	// }

	dev := devices[0]

	// cmdOutput, err := dev.RunShellCommand("monkey", "-p", "tv.danmaku.bili", "-c", "android.intent.category.LAUNCHER", "1")
	cmdOutput, err := dev.RunShellCommand("ls /sdcard")
	// cmdOutput, err := dev.RunShellCommandWithBytes("screencap -p")
	if err != nil {
		t.Fatal(dev.serial, err)
	}
	t.Log("\n⬇️"+dev.serial+"⬇️\n", cmdOutput)
}

func TestDevice_EnableAdbOverTCP(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	dev := devices[0]

	err = dev.EnableADBOverTCP()
	if err != nil {
		t.Fatal(err)
	}
}

func TestDevice_Exists(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	dev := devices[0]

	type args struct {
		path string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "exists",
			args: args{
				path: "/data/local/tmp/minicap.so",
			},
			want: true,
		},
		{
			name: "not exists",
			args: args{
				path: "/data/local/tmp/minicap.a",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := dev.Exists(tt.args.path)
				if got != tt.want {
					t.Fatalf("expected %v, but got %v", tt.want, got)
				}
				t.Logf("exists: %t", got)
			},
		)
	}
}

func TestDevice_Stat(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	dev := devices[0]

	type args struct {
		path string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "file",
			args: args{
				path: "/data/local/tmp/minicap.so",
			},
		},
		{
			name: "directory",
			args: args{
				path: "/data/local/tmp/x86_64",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				fi, err := dev.Stat(tt.args.path)
				if err != nil {
					t.Fatal(err)
				}

				t.Logf(
					"path: %s, name: %s, size: %d, mode: %s, file: %t, dir: %t, modified: %s",
					fi.Path(), fi.Name(), fi.Size(), fi.Mode(), fi.IsFile(), fi.IsDir(),
					fi.ModTime().Format("2006-01-02 15:04:05"),
				)
			},
		)
	}
}

func TestDevice_List(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	dev := devices[0]

	fileInfos, err := dev.List("/data/local/tmp")
	if err != nil {
		t.Fatal(err)
	}

	for _, fileInfo := range fileInfos {
		t.Log("info: ", fileInfo, ", dir: ", fileInfo.IsDir())
	}
}

func TestDevice_PushFile(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	dev := devices[0]

	tempDir, err := os.MkdirTemp("", "push_test_")
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		_ = os.RemoveAll(tempDir)
	}()

	type args struct {
		localFile  string
		content    string
		remotePath string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "push file",
			args: args{
				localFile:  filepath.Join(tempDir, "file1.txt"),
				content:    "this is test file 1\n",
				remotePath: "/data/local/tmp/push_test/file1.txt",
			},
		},
		{
			name: "push file and change name",
			args: args{
				localFile:  filepath.Join(tempDir, "file2.log"),
				content:    "this is test file 2\n",
				remotePath: "/data/local/tmp/push_test/push.txt",
			},
		},
		{
			name: "push file to a directory",
			args: args{
				localFile:  filepath.Join(tempDir, "file3"),
				content:    "this is test file 3\n",
				remotePath: "/data/local/tmp/push_test",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if err := os.MkdirAll(filepath.Dir(tt.args.localFile), 0o755); err != nil {
					t.Fatalf("failed to create local directory: %v", err)
				}

				if err := os.WriteFile(tt.args.localFile, []byte(tt.args.content), 0o644); err != nil {
					t.Fatalf("failed to write local file: %v", err)
				}

				err = dev.PushFile(tt.args.localFile, tt.args.remotePath, WithFileModTime(time.Now()))
				if err != nil {
					t.Fatalf("failed to push file: %v", err)
				}

				info, err := dev.Stat(tt.args.remotePath)
				if err != nil {
					t.Fatalf("failed to stat file: %v", err)
				}

				filePath := tt.args.remotePath
				if info.IsDir() {
					filePath = filepath.Join(tt.args.remotePath, filepath.Base(tt.args.localFile))
				}

				if !dev.Exists(filePath) {
					t.Fatalf("remote file %q does not exist", filePath)
				}

				buf := &bytes.Buffer{}
				if err := dev.ReadFile(filePath, buf); err != nil {
					t.Fatalf("failed to read remote file: %v", err)
				}

				gotContent := buf.String()
				if gotContent != tt.args.content {
					t.Fatalf("content mismatch for %q. expected %q, got %q", filePath, tt.args.content, gotContent)
				}

				if _, err := dev.RunShellCommand("rm", "-f", filePath); err != nil {
					t.Logf("Warning: failed to clean up remote file %q: %v", filePath, err)
				}

				t.Logf("PushFile test '%s' completed successfully", tt.name)
			},
		)
	}
}

func TestDevice_PushByReader(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	dev := devices[0]
	err = dev.PushByReader(
		strings.NewReader("world"), "/data/local/tmp/push_test/hello.txt", WithFileModTime(time.Now()),
	)
	if err != nil {
		t.Fatal(err)
	}
}

func TestDevice_PushDir(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	if len(devices) == 0 {
		t.Skip("No ADB devices available")
	}

	dev := devices[0]

	// Create a temporary local directory structure for testing
	tempDir, err := os.MkdirTemp("", "gadb_push_test_")
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		_ = os.RemoveAll(tempDir)
	}()

	type args struct {
		name       string
		localDir   string
		remotePath string
		testFiles  map[string]string
	}

	tests := []struct {
		name string
		args args
	}{
		{
			name: "push empty directory",
			args: args{
				localDir:   filepath.Join(tempDir, "empty_test"),
				remotePath: "/data/local/tmp/gadb_empty_test",
			},
		},
		{
			name: "push simple directory",
			args: args{
				localDir:   filepath.Join(tempDir, "simple_test"),
				remotePath: "/data/local/tmp/gadb_simple_test",
				testFiles: map[string]string{
					"file1.txt": "This is test file 1",
					"file2.so":  "Binary content for file2",
				},
			},
		},
		{
			name: "push nested directory",
			args: args{
				localDir:   filepath.Join(tempDir, "nested_test"),
				remotePath: "/data/local/tmp/gadb_nested_test",
				testFiles: map[string]string{
					"root_file.txt":             "Root level file",
					"subdir/nested_file.txt":    "This is a nested file",
					"subdir/deep/deep_file.txt": "Deep nested file",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				// Setup test directory structure
				if err := os.MkdirAll(tt.args.localDir, 0o755); err != nil {
					t.Fatal("Failed to create test directory:", err)
				}

				for fileName, content := range tt.args.testFiles {
					filePath := filepath.Join(tt.args.localDir, fileName)
					if err := os.MkdirAll(filepath.Dir(filePath), 0o755); err != nil {
						t.Fatal("Failed to create directory for file:", err)
					}
					if err := os.WriteFile(filePath, []byte(content), 0o644); err != nil {
						t.Fatal("Failed to create test file:", err)
					}
				}

				// Clean up remote directory first (ignore errors)
				_, _ = dev.RunShellCommand("rm", "-rf", tt.args.remotePath)

				// Push the directory
				err = dev.PushDir(tt.args.localDir, tt.args.remotePath)
				if err != nil {
					t.Fatal("Failed to push directory:", err)
				}

				// Verify files were pushed correctly
				for fileName, expectedContent := range tt.args.testFiles {
					remoteFile := filepath.Join(tt.args.remotePath, fileName)

					// Check if remote file exists
					if !dev.Exists(remoteFile) {
						t.Errorf("Remote file %s does not exist", remoteFile)
						continue
					}

					// Read remote file content and compare
					buf := &bytes.Buffer{}
					err := dev.ReadFile(remoteFile, buf)
					if err != nil {
						t.Errorf("Failed to read remote file %s: %v", remoteFile, err)
						continue
					}

					if buf.String() != expectedContent {
						t.Errorf(
							"Content mismatch for %s. Expected: %q, Got: %q",
							remoteFile, expectedContent, buf.String(),
						)
					}
				}

				// Clean up remote directory
				_, err = dev.RunShellCommand("rm", "-rf", tt.args.remotePath)
				if err != nil {
					t.Logf("Warning: Failed to clean up remote directory: %v", err)
				}

				t.Logf("PushDir test '%s' completed successfully", tt.name)
			},
		)
	}
}

func TestDevice_Pull(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	dev := devices[0]
	home, _ := os.UserHomeDir()

	type args struct {
		src string
		dst string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "pull file",
			args: args{
				src: "/sdcard/Download/test/hello.txt",
				dst: home + "/Downloads/pull_file.txt",
			},
		},
		{
			name: "pull directory",
			args: args{
				src: "/sdcard/fastbot_logs",
				dst: home + "/Downloads/pull_fastbot_logs",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				_ = os.RemoveAll(tt.args.dst)
				err = dev.Pull(tt.args.src, tt.args.dst)
				if err != nil {
					t.Fatal(err)
				}
			},
		)
	}
}

func TestDevice_ReadFile(t *testing.T) {
	adbClient, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	devices, err := adbClient.ListDevices()
	if err != nil {
		t.Fatal(err)
	}

	dev := devices[0]

	type args struct {
		src string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "max.activity.statistics.log",
			args: args{
				src: "/sdcard/fastbot/task_id_1/max.activity.statistics.log",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				buf := &bytes.Buffer{}
				if err = dev.ReadFile(tt.args.src, buf); err != nil {
					t.Fatal(err)
				}

				var m map[string]any
				if err = json.NewDecoder(buf).Decode(&m); err != nil {
					t.Fatal(err)
				}

				bs, _ := json.MarshalIndent(m, "", "  ")
				t.Logf("output: \n%s", bs)
			},
		)
	}
}
