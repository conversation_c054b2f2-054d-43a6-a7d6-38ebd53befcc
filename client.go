package gadb

import (
	"fmt"
	"os/exec"
	"strconv"
	"strings"
)

const (
	localhost = "localhost"

	ADBExecutableName = "adb"

	ADBServerPort = 5037
	ADBDaemonPort = 5555
)

type (
	Config struct {
		// Path to the adb executable. If empty, the PATH environment variable will be searched.
		PathOfADB string

		// Host and port the adb server is listening on.
		// If not specified, will use the default port on localhost.
		Host string
		Port int
	}

	Client struct {
		config   Config
		address  string
		isRemote bool
	}

	ClientOption  func(*clientOptions)
	clientOptions struct {
		port int
	}
)

func WithPort(port int) ClientOption {
	return func(o *clientOptions) {
		o.port = port
	}
}

func NewClient() (*Client, error) {
	return NewClientWithConfig(Config{})
}

func NewClientWithConfig(config Config) (*Client, error) {
	var (
		address string
		remote  bool
	)

	if config.Host == "" {
		config.Host = localhost
	}
	if config.Port == 0 {
		config.Port = ADBServerPort
	}
	address = fmt.Sprintf("%s:%d", config.Host, config.Port)

	if config.Host == localhost {
		if config.PathOfADB == "" {
			path, err := exec.LookPath(ADBExecutableName)
			if err != nil {
				return nil, fmt.Errorf("not find %s in PATH", ADBExecutableName)
			}

			config.PathOfADB = path
		}
	} else {
		remote = true

		// try to connect to remote adb server
		tp, err := newTransport(address)
		if err != nil {
			return nil, err
		}
		defer func() { _ = tp.Close() }()
	}

	return &Client{
		config:   config,
		address:  address,
		isRemote: remote,
	}, nil
}

func (c *Client) ServerVersion() (int, error) {
	resp, err := c.executeCommand(hostServiceOfVersion, false)
	if err != nil {
		return 0, err
	}

	v, err := strconv.ParseInt(resp, 16, 64)
	if err != nil {
		return 0, err
	}

	return int(v), nil
}

func (c *Client) ListDeviceSerials() ([]string, error) {
	resp, err := c.executeCommand(hostServiceOfDevices, false)
	if err != nil {
		return nil, err
	}

	infos, err := parseDeviceList(resp, parseDeviceShort)
	if err != nil {
		return nil, err
	}

	serials := make([]string, 0, len(infos))
	for _, info := range infos {
		serials = append(serials, info.Serial)
	}

	return serials, nil
}

func (c *Client) ListDevices() ([]*Device, error) {
	resp, err := c.executeCommand(hostServiceOfDevicesL, false)
	if err != nil {
		return nil, err
	}

	infos, err := parseDeviceList(resp, parseDeviceLong)
	if err != nil {
		return nil, err
	}

	devices := make([]*Device, 0, len(infos))
	for _, info := range infos {
		devices = append(
			devices, &Device{
				client: c,
				serial: info.Serial,
				info:   *info,
			},
		)
	}

	return devices, nil
}

func (c *Client) FindDeviceBySerial(serial string) (*Device, error) {
	devices, err := c.ListDevices()
	if err != nil {
		return nil, err
	}

	for _, device := range devices {
		if strings.EqualFold(device.serial, serial) {
			return device, nil
		}
	}

	return nil, fmt.Errorf("not found device by serial: %s", serial)
}

func (c *Client) ForwardList() ([]DeviceForward, error) {
	resp, err := c.executeCommand(hostServiceOfListForward, false)
	if err != nil {
		return nil, err
	}

	lines := strings.Split(resp, "\n")
	deviceForwards := make([]DeviceForward, 0, len(lines))
	for i := range lines {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) < 3 {
			continue
		}

		deviceForwards = append(deviceForwards, DeviceForward{Serial: fields[0], Local: fields[1], Remote: fields[2]})
	}

	return deviceForwards, nil
}

func (c *Client) ForwardKillAll() error {
	_, err := c.executeCommand(hostServiceOfKillForwardAll, true)
	return err
}

func (c *Client) Connect(ip string, opts ...ClientOption) error {
	o := &clientOptions{
		port: ADBDaemonPort,
	}
	for _, opt := range opts {
		opt(o)
	}

	resp, err := c.executeCommand(fmt.Sprintf(hostServiceOfConnectWithIPAndPort, ip, o.port), false)
	if err != nil {
		return err
	}

	if !strings.HasPrefix(resp, "connected to") && !strings.HasPrefix(resp, "already connected to") {
		return fmt.Errorf("adb connect: %s", resp)
	}

	return nil
}

func (c *Client) Disconnect(ip string, opts ...ClientOption) error {
	o := &clientOptions{}
	for _, opt := range opts {
		opt(o)
	}

	cmd := fmt.Sprintf(hostServiceOfDisconnectByIP, ip)
	if o.port != 0 {
		cmd = fmt.Sprintf(hostServiceOfDisconnectByIPAndPort, ip, o.port)
	}

	resp, err := c.executeCommand(cmd, false)
	if err != nil {
		return err
	}

	if !strings.HasPrefix(resp, "disconnected") {
		return fmt.Errorf("adb disconnect: %s", resp)
	}

	return nil
}

func (c *Client) DisconnectAll() error {
	resp, err := c.executeCommand(hostServiceOfDisconnect, false)
	if err != nil {
		return err
	}

	if !strings.HasPrefix(resp, "disconnected everything") {
		return fmt.Errorf("adb disconnect all: %s", resp)
	}

	return nil
}

func (c *Client) StartServerWithOutput() ([]byte, error) {
	if c.isRemote {
		return []byte(fmt.Sprintf("can not to start the remote adb server: %s", c.address)), nil
	}

	executable := ADBExecutableName
	if c.config.PathOfADB != "" {
		executable = c.config.PathOfADB
	}
	socket := fmt.Sprintf("tcp:%s", c.address)
	return exec.Command(executable, "-L", socket, adbCmdStartServer).CombinedOutput()
}

func (c *Client) StartServer() error {
	var (
		output []byte
		err    error
	)
	defer func() {
		if output != nil {
			debugLog(strings.TrimSpace(string(output)))
		}
	}()

	output, err = c.StartServerWithOutput()
	return err
}

func (c *Client) KillServer() error {
	tp, err := c.createTransport()
	if err != nil {
		return err
	}
	defer func() { _ = tp.Close() }()

	return tp.Send(hostServiceOfKill)
}

func (c *Client) createTransport() (*Transport, error) {
	return newTransport(c.address)
}

func (c *Client) executeCommand(command string, onlyVerifyResponse bool) (string, error) {
	tp, err := c.createTransport()
	if err != nil {
		return "", err
	}
	defer func() { _ = tp.Close() }()

	if err = tp.Send(command); err != nil {
		return "", err
	}
	if err = tp.VerifyResponse(); err != nil {
		return "", err
	}

	if onlyVerifyResponse {
		return "", nil
	}

	resp, err := tp.UnpackString()
	if err != nil {
		return "", err
	}

	return resp, nil
}
