package proxy

import (
	"fmt"
	"io"
)

const defaultReadPacketSize = 64 << 10 // 64KB

// PacketReaderError 数据包读取错误类型
type PacketReaderError struct {
	Type    string
	Message string
	Packet  *Packet
}

func (e *PacketReaderError) Error() string {
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// 错误类型常量
const (
	ErrorTypeChecksum = "ChecksumError"
	ErrorTypeMagic    = "MagicError"
	ErrorTypeIO       = "IOError"
	ErrorTypeParse    = "ParseError"
)

// NewChecksumError 创建校验和错误
func NewChecksumError(packet *Packet) *PacketReaderError {
	return &PacketReaderError{
		Type:    ErrorTypeChecksum,
		Message: "Checksum mismatch",
		Packet:  packet,
	}
}

// NewMagicError 创建魔数错误
func NewMagicError(packet *Packet) *PacketReaderError {
	return &PacketReaderError{
		Type:    ErrorTypeMagic,
		Message: "Magic value mismatch",
		Packet:  packet,
	}
}

// PacketReader 数据包读取器
type PacketReader struct {
	reader        io.Reader
	buffer        []byte
	inBody        bool
	currentPacket *Packet
}

// NewPacketReader 创建新的数据包读取器
func NewPacketReader(reader io.Reader) *PacketReader {
	return &PacketReader{
		reader: reader,
		buffer: make([]byte, 0),
	}
}

// ReadPacket 读取下一个数据包
func (pr *PacketReader) ReadPacket() (*Packet, error) {
	for {
		if pr.inBody {
			// 读取数据包体
			if len(pr.buffer) < int(pr.currentPacket.Length) {
				if err := pr.fillBuffer(); err != nil {
					return nil, err
				}
				continue
			}

			// 提取数据包体
			pr.currentPacket.SetData(pr.consume(int(pr.currentPacket.Length)))

			// 验证校验和
			if !pr.currentPacket.VerifyChecksum() {
				return nil, NewChecksumError(pr.currentPacket)
			}

			packet := pr.currentPacket
			pr.inBody = false
			pr.currentPacket = nil
			return packet, nil
		} else {
			// 读取数据包头
			if len(pr.buffer) < 24 {
				if err := pr.fillBuffer(); err != nil {
					return nil, err
				}
				continue
			}

			// 解析头部
			header := pr.consume(24)
			packet, err := ParsePacket(header)
			if err != nil {
				return nil, &PacketReaderError{
					Type:    ErrorTypeParse,
					Message: err.Error(),
				}
			}

			// 验证魔数
			if !packet.VerifyMagic() {
				return nil, NewMagicError(packet)
			}

			if packet.Length == 0 {
				// 没有数据体，直接返回
				return packet, nil
			} else {
				// 有数据体，进入读取数据体状态
				pr.currentPacket = packet
				pr.inBody = true
			}
		}
	}
}

// fillBuffer 从 reader 读取数据并追加到缓冲区
func (pr *PacketReader) fillBuffer() error {
	chunk := make([]byte, defaultReadPacketSize)
	n, err := pr.reader.Read(chunk)
	if err != nil {
		return err
	}

	if n > 0 {
		pr.buffer = append(pr.buffer, chunk[:n]...)
	}

	return nil
}

// consume 从缓冲区消费指定长度的数据
func (pr *PacketReader) consume(length int) []byte {
	if length > len(pr.buffer) {
		length = len(pr.buffer)
	}

	chunk := make([]byte, length)
	copy(chunk, pr.buffer[:length])

	if length == len(pr.buffer) {
		pr.buffer = pr.buffer[:0] // 清空缓冲区
	} else {
		pr.buffer = pr.buffer[length:] // 移除已消费的数据
	}

	return chunk
}
