package proxy

// Placeholder is a placeholder object that can be used globally.
var Placeholder PlaceholderType

type (
	// AnyType can be used to hold any type.
	AnyType = any
	// PlaceholderType represents a placeholder type.
	PlaceholderType = struct{}
)

// SocketOptions 套接字选项
type SocketOptions struct {
	// Auth 认证函数
	Auth func(key PublicKey) error
	// KnownPublicKeys 已知公钥列表
	KnownPublicKeys []PublicKey
}

// PublicKey 公钥接口
type PublicKey interface {
	// Verify 验证签名
	Verify(digest, signature string) bool
	// String 返回公钥字符串表示
	String() string
}
