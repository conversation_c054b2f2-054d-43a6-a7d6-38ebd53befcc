package proxy

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"testing"
	"time"

	"github.com/electricbubble/gadb"
)

func TestServer(t *testing.T) {
	c, err := gadb.NewClient()
	if err != nil {
		t.Fatal(err)
	}

	s := NewServer(c, "LZYTYLZT9HFI6DLN")
	if err = s.Listen("tcp", ":19998"); err != nil {
		t.<PERSON><PERSON>(err)
	}
	defer func() {
		_ = s.Close()
	}()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	signals := make(chan os.Signal, 1)
	signal.Notify(signals, syscall.SIGTERM, syscall.SIGINT)

	for {
		select {
		case v := <-signals:
			switch v {
			case syscall.SIGTERM, syscall.SIGINT:
				t.Logf("got a exit signal: %v", v)
				cancel()
			default:
				t.<PERSON><PERSON>rf("unexpected signal: %v", v)
			}
		case <-ctx.Done():
			t.Log("got a done signal")
			return
		}
	}
}

func TestHex(t *testing.T) {
	data := []byte{
		0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x6f, 0x70,
		0x70, 0x6f, 0x2f, 0x2e, 0x49, 0x6d, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x0a,
	}
	t.Logf("%x, %s", data, data)

	data = []byte{
		0x73, 0x68, 0x65, 0x6c, 0x6c, 0x3a, 0x69, 0x6d, 0x65, 0x20, 0x6c, 0x69, 0x73, 0x74, 0x20, 0x2d, 0x73,
	}
	t.Logf("%x, %s", data, data)

	data = []byte{
		0x73, 0x68, 0x65, 0x6c, 0x6c, 0x3a,
	}
	t.Logf("%x, %s", data, data)

	data = []byte{176, 180, 190, 166}
	t.Logf("%x, %s", data, data)

	data = []byte{188, 179, 172, 186}
	t.Logf("%x, %s", data, data)
}
