package proxy

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"sync"

	"github.com/electricbubble/gadb"
)

// ServiceError 服务错误类型
type ServiceError struct {
	Type    string
	Message string
	Packet  *Packet
}

func (e *ServiceError) Error() string {
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// 错误类型常量
const (
	ServiceErrorTypePrematurePacket = "PrematurePacketError"
	ServiceErrorTypeLateTransport   = "LateTransportError"
)

// NewPrematurePacketError 创建过早数据包错误
func NewPrematurePacketError(packet *Packet) *ServiceError {
	return &ServiceError{
		Type:    ServiceErrorTypePrematurePacket,
		Message: "Premature packet",
		Packet:  packet,
	}
}

// NewLateTransportError 创建延迟传输错误
func NewLateTransportError() *ServiceError {
	return &ServiceError{
		Type:    ServiceErrorTypeLateTransport,
		Message: "Late transport",
	}
}

// Service ADB 服务处理器
type Service struct {
	*slog.Logger

	mu       sync.RWMutex
	client   *gadb.Client
	serial   string
	localID  uint32
	remoteID uint32
	socket   *Socket

	opened    bool
	closed    bool
	needAck   bool
	transport *gadb.Transport

	// 控制
	ctx       context.Context
	cancel    context.CancelFunc
	done      chan PlaceholderType
	closeOnce sync.Once
}

// NewService 创建新的服务处理器
func NewService(client *gadb.Client, serial string, localID, remoteID uint32, socket *Socket) *Service {
	ctx, cancel := context.WithCancel(context.Background())

	return &Service{
		Logger: slog.New(
			slog.NewJSONHandler(
				os.Stdout, &slog.HandlerOptions{
					// AddSource: true,
					Level: socket.options.loggerLevel,
				},
			),
		),

		client:   client,
		serial:   serial,
		localID:  localID,
		remoteID: remoteID,
		socket:   socket,

		ctx:    ctx,
		cancel: cancel,
		done:   make(chan PlaceholderType),
	}
}

// Close 关闭服务
func (s *Service) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	return s.close()
}

// close 内部关闭方法，不加锁
// 注意：调用此方法前必须已经获取了写锁
func (s *Service) close() error {
	s.closeOnce.Do(
		func() {
			if s.closed {
				return
			}

			// 关闭传输连接
			if s.transport != nil {
				_ = s.transport.Close()
			}

			// 发送关闭数据包
			localID := uint32(0)
			if s.opened {
				localID = s.localID
			}

			if s.socket != nil {
				_ = s.socket.SendPacket(NewPacket(CommandOfCLSE, localID, s.remoteID, nil))
			}

			s.transport = nil
			s.closed = true
			s.cancel()

			close(s.done)
		},
	)

	return nil
}

// GetID 获取服务 ID
func (s *Service) GetID() uint32 {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.localID
}

// IsClosed 检查服务是否已关闭
func (s *Service) IsClosed() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.closed
}

// Handle 处理数据包
func (s *Service) Handle(packet *Packet) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.closed {
		return nil
	}

	switch packet.Command {
	case CommandOfOPEN:
		return s.handleOpenPacket(packet)
	case CommandOfOKAY:
		return s.handleOkayPacket(packet)
	case CommandOfWRTE:
		return s.handleWritePacket(packet)
	case CommandOfCLSE:
		return s.handleClosePacket(packet)
	default:
		return fmt.Errorf("unexpected packet command: 0x%08x", packet.Command)
	}
}

// handleOpenPacket 处理Open数据包
func (s *Service) handleOpenPacket(packet *Packet) error {
	if s.transport != nil {
		return fmt.Errorf("service already opened")
	}

	// 获取设备连接
	device, err := s.client.FindDeviceBySerial(s.serial)
	if err != nil {
		return fmt.Errorf("failed to get device: %w", err)
	}

	// 创建传输连接
	s.transport, err = device.CreateDeviceTransport()
	if err != nil {
		return fmt.Errorf("failed to create transport: %w", err)
	}

	if s.closed {
		return NewLateTransportError()
	}

	// 发送服务名称（去掉末尾的空字节）
	serviceName := packet.Data
	if len(serviceName) > 0 && serviceName[len(serviceName)-1] == 0 {
		serviceName = serviceName[:len(serviceName)-1]
	}

	if err = s.transport.SendCommand(string(serviceName)); err != nil {
		return fmt.Errorf("failed to write service name: %w", err)
	}

	if err = s.transport.VerifyResponse(); err != nil {
		return fmt.Errorf("failed to verify response: %w", err)
	}

	// 发送 OKAY 响应
	if err = s.socket.SendPacket(NewPacket(CommandOfOKAY, s.localID, s.remoteID, nil)); err != nil {
		return fmt.Errorf("failed to write okay packet: %w", err)
	}
	s.opened = true

	// 启动数据转发
	go s.forwardData()
	return nil
}

// handleOkayPacket 处理确认数据包
func (s *Service) handleOkayPacket(packet *Packet) error {
	if s.closed {
		return nil
	}

	if s.transport == nil {
		return NewPrematurePacketError(packet)
	}

	s.needAck = false
	return nil
}

// handleWritePacket 处理写入数据包
func (s *Service) handleWritePacket(packet *Packet) error {
	if s.closed {
		return nil
	}

	if s.transport == nil {
		return NewPrematurePacketError(packet)
	}

	// 转发数据到传输连接
	if len(packet.Data) > 0 {
		if err := s.transport.SendData(packet.Data); err != nil {
			return fmt.Errorf("failed to write data to transport: %w", err)
		}
	}

	// 发送 OKAY 响应
	if err := s.socket.SendPacket(NewPacket(CommandOfOKAY, s.localID, s.remoteID, nil)); err != nil {
		return fmt.Errorf("failed to write okay packet: %w", err)
	}

	return nil
}

// handleClosePacket 处理关闭数据包
func (s *Service) handleClosePacket(packet *Packet) error {
	if s.closed {
		return nil
	}

	if s.transport == nil {
		return NewPrematurePacketError(packet)
	}

	// 调用内部关闭方法，避免死锁（因为 Handle 方法已经持有锁）
	return s.close()
}

// forwardData 转发数据从传输连接到套接字
func (s *Service) forwardData() {
	defer func() {
		if s != nil {
			_ = s.close()
		}
	}()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-s.done:
			return
		default:
			s.mu.RLock()
			needAck := s.needAck
			closed := s.closed
			s.mu.RUnlock()

			if needAck {
				continue
			}
			if closed {
				return
			}

			// 从传输连接读取数据
			buffer, err := s.transport.TryReadBytes(gadb.WithTryReadSize(int(s.socket.maxPayload)))
			if err != nil {
				return
			}

			n := len(buffer)
			if n > 0 {
				s.mu.Lock()
				s.needAck = true
				s.mu.Unlock()

				// 发送写入数据包
				data := make([]byte, n)
				copy(data, buffer)

				if err = s.socket.SendPacket(NewPacket(CommandOfWRTE, s.localID, s.remoteID, data)); err != nil {
					s.Error("failed to write data to socket", "error", err)
					return // 写入错误，结束转发
				}
			}
		}
	}
}
