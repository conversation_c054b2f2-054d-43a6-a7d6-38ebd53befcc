package proxy

import (
	"bytes"
	"testing"
)

func TestPacket_NewPacket(t *testing.T) {
	tests := []struct {
		name     string
		command  Command
		arg0     uint32
		arg1     uint32
		data     []byte
		expected *Packet
	}{
		{
			name:    "SYNC packet without data",
			command: CommandOfSYNC,
			arg0:    1,
			arg1:    2,
			data:    nil,
			expected: &Packet{
				Command: CommandOfSYNC,
				Arg0:    1,
				Arg1:    2,
				Length:  0,
				Check:   0,
				Magic:   magic(CommandOfSYNC),
				Data:    nil,
			},
		},
		{
			name:    "OPEN packet with data",
			command: CommandOfOPEN,
			arg0:    100,
			arg1:    200,
			data:    []byte("shell:"),
			expected: &Packet{
				Command: CommandOfOPEN,
				Arg0:    100,
				Arg1:    200,
				Length:  6,
				Check:   checksum([]byte("shell:")),
				Magic:   magic(CommandOfOPEN),
				Data:    []byte("shell:"),
			},
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				packet := NewPacket(tt.command, tt.arg0, tt.arg1, tt.data)

				if packet.Command != tt.expected.Command {
					t.Errorf("Command = %v, want %v", packet.Command, tt.expected.Command)
				}
				if packet.Arg0 != tt.expected.Arg0 {
					t.Errorf("Arg0 = %v, want %v", packet.Arg0, tt.expected.Arg0)
				}
				if packet.Arg1 != tt.expected.Arg1 {
					t.Errorf("Arg1 = %v, want %v", packet.Arg1, tt.expected.Arg1)
				}
				if packet.Length != tt.expected.Length {
					t.Errorf("Length = %v, want %v", packet.Length, tt.expected.Length)
				}
				if packet.Check != tt.expected.Check {
					t.Errorf("Check = %v, want %v", packet.Check, tt.expected.Check)
				}
				if packet.Magic != tt.expected.Magic {
					t.Errorf("magic = %v, want %v", packet.Magic, tt.expected.Magic)
				}
				if !bytes.Equal(packet.Data, tt.expected.Data) {
					t.Errorf("Data = %v, want %v", packet.Data, tt.expected.Data)
				}
			},
		)
	}
}

func TestChecksum(t *testing.T) {
	tests := []struct {
		name     string
		data     []byte
		expected uint32
	}{
		{
			name:     "nil data",
			data:     nil,
			expected: 0,
		},
		{
			name:     "empty data",
			data:     []byte{},
			expected: 0,
		},
		{
			name:     "single byte",
			data:     []byte{1},
			expected: 1,
		},
		{
			name:     "multiple bytes",
			data:     []byte{1, 2, 3, 4, 5},
			expected: 15,
		},
		{
			name:     "shell command",
			data:     []byte("shell:"),
			expected: 115 + 104 + 101 + 108 + 108 + 58, // sum of ASCII values
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				result := checksum(tt.data)
				if result != tt.expected {
					t.Errorf("checksum() = %v, want %v", result, tt.expected)
				}
			},
		)
	}
}

func TestMagic(t *testing.T) {
	tests := []struct {
		name     string
		command  Command
		expected uint32
	}{
		{
			name:     "SYNC command",
			command:  CommandOfSYNC,
			expected: uint32(CommandOfSYNC ^ 0xffffffff),
		},
		{
			name:     "CNXN command",
			command:  CommandOfCNXN,
			expected: uint32(CommandOfCNXN ^ 0xffffffff),
		},
		{
			name:     "OPEN command",
			command:  CommandOfOPEN,
			expected: uint32(CommandOfOPEN ^ 0xffffffff),
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				result := magic(tt.command)
				if result != tt.expected {
					t.Errorf("magic() = %v, want %v", result, tt.expected)
				}
			},
		)
	}
}

func TestAssemble(t *testing.T) {
	tests := []struct {
		name     string
		command  Command
		arg0     uint32
		arg1     uint32
		data     []byte
		expected int // expected length
	}{
		{
			name:     "packet without data",
			command:  CommandOfSYNC,
			arg0:     1,
			arg1:     2,
			data:     nil,
			expected: 24, // header only
		},
		{
			name:     "packet with data",
			command:  CommandOfOPEN,
			arg0:     100,
			arg1:     200,
			data:     []byte("shell:"),
			expected: 30, // header + 6 bytes data
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				result := Assemble(tt.command, tt.arg0, tt.arg1, tt.data)
				if len(result) != tt.expected {
					t.Errorf("Assemble() length = %v, want %v", len(result), tt.expected)
				}

				// 验证可以正确解析
				if len(result) >= 24 {
					packet, err := ParsePacket(result[:24])
					if err != nil {
						t.Errorf("ParsePacket() error = %v", err)
						return
					}

					if packet.Command != tt.command {
						t.Errorf("Parsed command = %v, want %v", packet.Command, tt.command)
					}
					if packet.Arg0 != tt.arg0 {
						t.Errorf("Parsed arg0 = %v, want %v", packet.Arg0, tt.arg0)
					}
					if packet.Arg1 != tt.arg1 {
						t.Errorf("Parsed arg1 = %v, want %v", packet.Arg1, tt.arg1)
					}
				}
			},
		)
	}
}

func TestPacket_VerifyChecksum(t *testing.T) {
	tests := []struct {
		name     string
		packet   *Packet
		expected bool
	}{
		{
			name: "valid checksum",
			packet: &Packet{
				Check: checksum([]byte("test")),
				Data:  []byte("test"),
			},
			expected: true,
		},
		{
			name: "invalid checksum",
			packet: &Packet{
				Check: 999,
				Data:  []byte("test"),
			},
			expected: false,
		},
		{
			name: "zero checksum",
			packet: &Packet{
				Check: 0,
				Data:  []byte("test"),
			},
			expected: true, // zero checksum is always valid
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				result := tt.packet.VerifyChecksum()
				if result != tt.expected {
					t.Errorf("VerifyChecksum() = %v, want %v", result, tt.expected)
				}
			},
		)
	}
}

func TestPacket_VerifyMagic(t *testing.T) {
	tests := []struct {
		name     string
		packet   *Packet
		expected bool
	}{
		{
			name: "valid magic",
			packet: &Packet{
				Command: CommandOfSYNC,
				Magic:   magic(CommandOfSYNC),
			},
			expected: true,
		},
		{
			name: "invalid magic",
			packet: &Packet{
				Command: CommandOfSYNC,
				Magic:   999,
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				result := tt.packet.VerifyMagic()
				if result != tt.expected {
					t.Errorf("VerifyMagic() = %v, want %v", result, tt.expected)
				}
			},
		)
	}
}

func TestPacket_GetType(t *testing.T) {
	tests := []struct {
		name     string
		command  Command
		expected string
	}{
		{"SYNC", CommandOfSYNC, "SYNC"},
		{"CNXN", CommandOfCNXN, "CNXN"},
		{"OPEN", CommandOfOPEN, "OPEN"},
		{"OKAY", CommandOfOKAY, "OKAY"},
		{"CLSE", CommandOfCLSE, "CLSE"},
		{"WRTE", CommandOfWRTE, "WRTE"},
		{"AUTH", CommandOfAUTH, "AUTH"},
		{"UNKNOWN", 0x12345678, "UNKNOWN(0x12345678)"},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				packet := &Packet{Command: tt.command}
				result := packet.GetType()
				if result != tt.expected {
					t.Errorf("GetType() = %v, want %v", result, tt.expected)
				}
			},
		)
	}
}

func TestPacket_IsValid(t *testing.T) {
	tests := []struct {
		name     string
		packet   *Packet
		expected bool
	}{
		{
			name: "valid packet",
			packet: &Packet{
				Command: CommandOfSYNC,
				Magic:   magic(CommandOfSYNC),
				Check:   0,
				Data:    nil,
			},
			expected: true,
		},
		{
			name: "invalid magic",
			packet: &Packet{
				Command: CommandOfSYNC,
				Magic:   999,
				Check:   0,
				Data:    nil,
			},
			expected: false,
		},
		{
			name: "invalid checksum",
			packet: &Packet{
				Command: CommandOfSYNC,
				Magic:   magic(CommandOfSYNC),
				Check:   999,
				Data:    []byte("test"),
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				result := tt.packet.IsValid()
				if result != tt.expected {
					t.Errorf("IsValid() = %v, want %v", result, tt.expected)
				}
			},
		)
	}
}
