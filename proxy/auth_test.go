package proxy

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha1"
	"encoding/base64"
	"encoding/binary"
	"fmt"
	"testing"
)

// generateTestRSAKey 生成测试用的RSA密钥对
func generateTestRSAKey() (*rsa.PrivateKey, error) {
	return rsa.GenerateKey(rand.Reader, 2048)
}

// createAndroidPublicKey 创建Android格式的公钥
func createAndroidPublicKey(pubKey *rsa.PublicKey) ([]byte, error) {
	// 将RSA公钥转换为Android格式
	modulus := pubKey.N.Bytes()

	// 确保模数是256字节（2048位）
	if len(modulus) > AndroidPubkeyModulusSize {
		return nil, ErrInvalidKeySize
	}

	// 创建Android格式的公钥结构
	var androidKey AndroidRSAPublicKey
	androidKey.ModulusSizeWords = AndroidPubkeyModulusSizeWord
	androidKey.Exponent = uint32(pubKey.E)

	// 将大端序的模数转换为小端序并填充到固定大小的数组中
	for i := 0; i < len(modulus); i++ {
		androidKey.Modulus[i] = modulus[len(modulus)-1-i]
	}

	// 序列化为字节数组
	result := make([]byte, 4+4+AndroidPubkeyModulusSize+AndroidPubkeyModulusSize+4)
	offset := 0

	// ModulusSizeWords (小端序)
	binary.LittleEndian.PutUint32(result[offset:], androidKey.ModulusSizeWords)
	offset += 4

	// N0Inv (小端序) - 简化实现，设为0
	binary.LittleEndian.PutUint32(result[offset:], 0)
	offset += 4

	// Modulus (小端序)
	copy(result[offset:], androidKey.Modulus[:])
	offset += AndroidPubkeyModulusSize

	// RR (小端序) - 简化实现，设为0
	copy(result[offset:], androidKey.RR[:])
	offset += AndroidPubkeyModulusSize

	// Exponent (小端序)
	binary.LittleEndian.PutUint32(result[offset:], androidKey.Exponent)

	return result, nil
}

var ErrInvalidKeySize = fmt.Errorf("invalid key size")

func TestNewPublicKey(t *testing.T) {
	// 生成测试密钥
	privateKey, err := generateTestRSAKey()
	if err != nil {
		t.Fatalf("Failed to generate test key: %v", err)
	}

	// 创建Android格式的公钥
	androidKeyBytes, err := createAndroidPublicKey(&privateKey.PublicKey)
	if err != nil {
		t.Fatalf("Failed to create Android public key: %v", err)
	}

	// 编码为base64
	keyData := base64.StdEncoding.EncodeToString(androidKeyBytes)

	// 测试NewPublicKey
	pubKey := NewPublicKey(keyData)
	if pubKey == nil {
		t.Fatal("NewPublicKey returned nil")
	}

	// 检查类型
	rsaKey, ok := pubKey.(*RSAPublicKey)
	if !ok {
		t.Fatal("NewPublicKey did not return *RSAPublicKey")
	}

	// 检查是否有效
	if !rsaKey.IsValid() {
		t.Fatal("Generated key is not valid")
	}

	// 检查String方法
	if rsaKey.String() != keyData {
		t.Error("String() method does not return original key data")
	}
}

func TestRSAPublicKey_Verify(t *testing.T) {
	// 生成测试密钥
	privateKey, err := generateTestRSAKey()
	if err != nil {
		t.Fatalf("Failed to generate test key: %v", err)
	}

	// 创建Android格式的公钥
	androidKeyBytes, err := createAndroidPublicKey(&privateKey.PublicKey)
	if err != nil {
		t.Fatalf("Failed to create Android public key: %v", err)
	}

	// 编码为base64
	keyData := base64.StdEncoding.EncodeToString(androidKeyBytes)

	// 创建公钥实例
	pubKey := NewPublicKey(keyData)
	rsaKey, ok := pubKey.(*RSAPublicKey)
	if !ok {
		t.Fatal("NewPublicKey did not return *RSAPublicKey")
	}

	// 创建测试数据
	testData := "test-token-data"

	// 使用私钥签名
	hasher := sha1.New()
	hasher.Write([]byte(testData))
	hashed := hasher.Sum(nil)

	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA1, hashed)
	if err != nil {
		t.Fatalf("Failed to sign test data: %v", err)
	}

	// 测试验证
	if !rsaKey.Verify(testData, string(signature)) {
		t.Error("Signature verification failed")
	}

	// 测试错误的签名
	wrongSignature := make([]byte, len(signature))
	copy(wrongSignature, signature)
	wrongSignature[0] ^= 0xFF // 修改第一个字节

	if rsaKey.Verify(testData, string(wrongSignature)) {
		t.Error("Wrong signature should not verify")
	}

	// 测试错误的数据
	if rsaKey.Verify("wrong-data", string(signature)) {
		t.Error("Signature should not verify with wrong data")
	}
}

func TestRSAPublicKey_InvalidKey(t *testing.T) {
	// 测试无效的base64数据
	invalidKey := NewPublicKey("invalid-base64-data!")
	rsaKey, ok := invalidKey.(*RSAPublicKey)
	if !ok {
		t.Fatal("NewPublicKey did not return *RSAPublicKey")
	}

	if rsaKey.IsValid() {
		t.Error("Invalid key should not be valid")
	}

	if rsaKey.Verify("test", "signature") {
		t.Error("Invalid key should not verify any signature")
	}
}

func TestRSAPublicKey_GetFingerprint(t *testing.T) {
	// 生成测试密钥
	privateKey, err := generateTestRSAKey()
	if err != nil {
		t.Fatalf("Failed to generate test key: %v", err)
	}

	// 创建Android格式的公钥
	androidKeyBytes, err := createAndroidPublicKey(&privateKey.PublicKey)
	if err != nil {
		t.Fatalf("Failed to create Android public key: %v", err)
	}

	// 编码为base64
	keyData := base64.StdEncoding.EncodeToString(androidKeyBytes)

	// 创建公钥实例
	pubKey := NewPublicKey(keyData)
	rsaKey, ok := pubKey.(*RSAPublicKey)
	if !ok {
		t.Fatal("NewPublicKey did not return *RSAPublicKey")
	}

	// 获取指纹
	fingerprint := rsaKey.GetFingerprint()
	if fingerprint == "invalid-key" {
		t.Error("Valid key should not return 'invalid-key' fingerprint")
	}

	// 指纹应该包含冒号分隔的十六进制字符
	if len(fingerprint) == 0 {
		t.Error("Fingerprint should not be empty")
	}

	t.Logf("Generated fingerprint: %s", fingerprint)
}
