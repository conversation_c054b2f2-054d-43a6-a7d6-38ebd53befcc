package proxy

import "log/slog"

const (
	defaultMaxConnections = 1000
	constMaxConnections   = 10000
)

type (
	Option  func(*options)
	options struct {
		maxConnections int64
		socketOptions  *SocketOptions
		loggerLevel    slog.Level
	}
)

func WithMaxConnections(maxConnections int64) Option {
	return func(o *options) {
		if maxConnections <= 0 {
			o.maxConnections = defaultMaxConnections
		} else if maxConnections > constMaxConnections {
			o.maxConnections = constMaxConnections
		} else {
			o.maxConnections = maxConnections
		}
	}
}

func WithSocketOptions(socketOptions *SocketOptions) Option {
	return func(o *options) {
		o.socketOptions = socketOptions
	}
}

func WithLoggerLevel(level slog.Level) Option {
	return func(o *options) {
		o.loggerLevel = level
	}
}

func defaultOptions() *options {
	return &options{
		maxConnections: defaultMaxConnections,
		socketOptions:  &SocketOptions{},
		loggerLevel:    slog.LevelError,
	}
}
