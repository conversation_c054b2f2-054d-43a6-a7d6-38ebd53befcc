package proxy

import (
	"crypto"
	"crypto/md5"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/x509"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
)

const (
	AndroidPubkeyModulusSize     = 256 // 2048位RSA密钥的模数大小（字节）
	AndroidPubkeyModulusSizeWord = 64  // 模数大小（32位字）

	exponent3     = 3
	exponent65537 = 65537
)

// RSAPublicKey 实现PublicKey接口的RSA公钥
type RSAPublicKey struct {
	keyData string // 原始公钥数据（可能包含注释）
	key     string // base64编码的公钥数据
	comment string // 注释

	len         uint32 // 模数长度（32位字数）
	n0inv       uint32 // 预计算的蒙哥马利参数: -1 / n[0] mod 2^32
	n           []byte // RSA模数（小端序数组）
	rr          []byte // 蒙哥马利参数 R^2（小端序数组）
	exponent    uint32 // RSA指数（3或65537）
	fingerprint string // 指纹

	pubKey *rsa.PublicKey // 解析后的RSA公钥
}

// NewPublicKey 从字符串创建公钥实例
func NewPublicKey(keyData string) (*RSAPublicKey, error) {
	rsaKey := &RSAPublicKey{
		keyData: keyData,
	}

	// 尝试解析公钥
	if err := rsaKey.parseKey(); err != nil {
		return nil, err
	}

	return rsaKey, nil
}

// parseKey 解析ADB格式的RSA公钥
func (r *RSAPublicKey) parseKey() error {
	ss := strings.Split(r.keyData, " ")
	if len(ss) == 0 {
		return fmt.Errorf("invalid public key format")
	}

	r.key = ss[0]
	if len(ss) > 1 {
		r.comment = strings.TrimSpace(ss[1])
	}

	// ADB公钥通常是base64编码的
	decoded, err := base64.StdEncoding.DecodeString(r.key)
	if err != nil {
		return fmt.Errorf("failed to decode public key: %w", err)
	}

	offset := 0

	// 读取长度（小端序）
	r.len = binary.LittleEndian.Uint32(decoded[offset:])
	offset += 4

	// 验证数据长度
	actualLen := len(decoded)
	expectedLen := 4 + 4 + (r.len+r.len)*4 + 4
	if actualLen != int(expectedLen) {
		return fmt.Errorf("invalid public key length, expected %d, but got %d", expectedLen, actualLen)
	}

	// 读取N0Inv（小端序）
	r.n0inv = binary.LittleEndian.Uint32(decoded[offset:])
	offset += 4

	// 读取模数（小端序）
	r.n = make([]byte, r.len*4)
	copy(r.n, decoded[offset:offset+int(r.len*4)])
	offset += int(r.len * 4)

	// 读取rr（小端序）
	r.rr = make([]byte, r.len*4)
	copy(r.rr, decoded[offset:offset+int(r.len*4)])
	offset += int(r.len * 4)

	// 读取指数（小端序）
	r.exponent = binary.LittleEndian.Uint32(decoded[offset:])
	if r.exponent != exponent3 && r.exponent != exponent65537 {
		return fmt.Errorf(
			"invalid public key exponent, expected %d or %d, but got %d", exponent3, exponent65537, r.exponent,
		)
	}

	// 计算指纹
	hash := md5.New()
	hash.Write(decoded)
	fingerprint := hex.EncodeToString(hash.Sum(nil))
	for i := 0; i < len(fingerprint); i += 2 {
		if i+2 < len(fingerprint) {
			r.fingerprint += fingerprint[i:i+2] + ":"
		} else {
			r.fingerprint += fingerprint[i : i+2]
		}
	}

	// 生成RSA公钥
	return r.generateRSAKey()
}

// generateRSAKey 生成RSA公钥
func (r *RSAPublicKey) generateRSAKey() error {
	// 将小端序的模数转换为大整数
	// Android使用小端序存储，但Go的big.Int需要大端序
	size := int(r.len * 4)
	modulus := make([]byte, size)
	for i := 0; i < size; i++ {
		modulus[size-1-i] = r.n[i]
	}

	// 创建标准RSA公钥
	r.pubKey = &rsa.PublicKey{
		N: new(big.Int).SetBytes(modulus),
		E: int(r.exponent),
	}

	return nil
}

// Verify 验证签名
// digest: 要验证的数据（通常是token）
// signature: 签名数据（二进制字符串格式）
func (r *RSAPublicKey) Verify(digest, signature string) bool {
	if r.pubKey == nil {
		return false
	}

	err := rsa.VerifyPKCS1v15(r.pubKey, crypto.SHA1, []byte(digest), []byte(signature))
	return err == nil
}

// String 返回公钥的字符串表示
func (r *RSAPublicKey) String() string {
	if r.keyData == "" {
		return "invalid-key"
	}

	// 返回原始的base64编码数据
	return r.keyData
}

// GetFingerprint 获取公钥指纹（用于显示给用户确认）
func (r *RSAPublicKey) GetFingerprint() string {
	if r.pubKey == nil {
		return "invalid-key"
	}

	// 将公钥转换为DER格式
	derBytes, err := x509.MarshalPKIXPublicKey(r.pubKey)
	if err != nil {
		return "invalid-key"
	}

	// 计算SHA256哈希作为指纹
	hasher := sha1.New()
	hasher.Write(derBytes)
	hash := hasher.Sum(nil)

	// 格式化为十六进制字符串，用冒号分隔
	var parts []string
	for _, b := range hash {
		parts = append(parts, fmt.Sprintf("%02x", b))
	}

	return strings.Join(parts, ":")
}

// IsValid 检查公钥是否有效
func (r *RSAPublicKey) IsValid() bool {
	return r.pubKey != nil
}
