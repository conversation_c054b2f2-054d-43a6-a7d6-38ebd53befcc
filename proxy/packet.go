package proxy

import (
	"bytes"
	"encoding/binary"
	"fmt"
)

const defaultPacketHeaderLength = 24

type Command uint32

// ADB 协议命令常量
const (
	CommandOfSYNC Command = 0x434e5953 // SYNC
	CommandOfCNXN Command = 0x4e584e43 // CNXN
	CommandOfOPEN Command = 0x4e45504f // OPEN
	CommandOfOKAY Command = 0x59414b4f // OKAY
	CommandOfCLSE Command = 0x45534c43 // CLSE
	CommandOfWRTE Command = 0x45545257 // WRTE
	CommandOfAUTH Command = 0x48545541 // AUTH
)

func (c Command) String() string {
	switch c {
	case CommandOfSYNC:
		return "SYNC"
	case CommandOfCNXN:
		return "CNXN"
	case CommandOfOPEN:
		return "OPEN"
	case CommandOfOKAY:
		return "OKAY"
	case CommandOfCLSE:
		return "CLSE"
	case CommandOfWRTE:
		return "WRTE"
	case CommandOfAUTH:
		return "AUTH"
	default:
		return fmt.Sprintf("UNKNOWN(0x%08x)", uint32(c))
	}
}

// Packet 表示 ADB 协议数据包
type Packet struct {
	Command Command // 命令类型
	Arg0    uint32  // 第一个参数
	Arg1    uint32  // 第二个参数
	Length  uint32  // 数据长度
	Check   uint32  // 校验和
	Magic   uint32  // 魔数
	Data    []byte  // 数据内容
}

// NewPacket 创建新的数据包
func NewPacket(command Command, arg0, arg1 uint32, data []byte) *Packet {
	return &Packet{
		Command: command,
		Arg0:    arg0,
		Arg1:    arg1,
		Length:  uint32(len(data)),
		Check:   checksum(data),
		Magic:   magic(command),
		Data:    data,
	}
}

// checksum 计算数据的校验和
func checksum(data []byte) uint32 {
	var sum uint32
	for _, b := range data {
		sum += uint32(b)
	}
	return sum
}

// magic 计算命令的魔数
func magic(command Command) uint32 {
	return uint32(command ^ 0xffffffff)
}

// Assemble 组装数据包为字节流
// Deprecated: use `NewPacket().ToBytes()` instead.
func Assemble(command Command, arg0, arg1 uint32, data []byte) []byte {
	length := uint32(0)
	if data != nil {
		length = uint32(len(data))
	}

	totalSize := defaultPacketHeaderLength
	if data != nil {
		totalSize += len(data)
	}

	buf := make([]byte, totalSize)

	// 写入头部信息（小端序）
	binary.LittleEndian.PutUint32(buf[0:4], uint32(command))
	binary.LittleEndian.PutUint32(buf[4:8], arg0)
	binary.LittleEndian.PutUint32(buf[8:12], arg1)
	binary.LittleEndian.PutUint32(buf[12:16], length)
	binary.LittleEndian.PutUint32(buf[16:20], checksum(data))
	binary.LittleEndian.PutUint32(buf[20:24], magic(command))

	// 写入数据
	if len(data) > 0 {
		copy(buf[24:], data)
	}

	return buf
}

// Swap32 交换字节序
func Swap32(n uint32) uint32 {
	buf := make([]byte, 4)
	binary.LittleEndian.PutUint32(buf, n)
	return binary.BigEndian.Uint32(buf)
}

// VerifyChecksum 验证校验和
func (p *Packet) VerifyChecksum() bool {
	if p.Check == 0 {
		return true
	}
	return p.Check == checksum(p.Data)
}

// VerifyMagic 验证魔数
func (p *Packet) VerifyMagic() bool {
	return p.Magic == magic(p.Command)
}

// GetType 获取命令类型字符串
func (p *Packet) GetType() string {
	switch p.Command {
	case CommandOfSYNC:
		return "SYNC"
	case CommandOfCNXN:
		return "CNXN"
	case CommandOfOPEN:
		return "OPEN"
	case CommandOfOKAY:
		return "OKAY"
	case CommandOfCLSE:
		return "CLSE"
	case CommandOfWRTE:
		return "WRTE"
	case CommandOfAUTH:
		return "AUTH"
	default:
		return fmt.Sprintf("UNKNOWN(0x%08x)", p.Command)
	}
}

// String 返回数据包的字符串表示
func (p *Packet) String() string {
	if len(p.Data) > 64 {
		return fmt.Sprintf("%s arg0=%d arg1=%d length=%d data=%s...", p.Command, p.Arg0, p.Arg1, p.Length, p.Data[:64])
	}

	return fmt.Sprintf("%s arg0=%d arg1=%d length=%d data=%s", p.Command, p.Arg0, p.Arg1, p.Length, p.Data)
}

// ToBytes 将数据包转换为字节流
func (p *Packet) ToBytes() []byte {
	buf := new(bytes.Buffer)

	// 写入头部信息（小端序）
	_ = binary.Write(buf, binary.LittleEndian, uint32(p.Command))
	_ = binary.Write(buf, binary.LittleEndian, p.Arg0)
	_ = binary.Write(buf, binary.LittleEndian, p.Arg1)
	_ = binary.Write(buf, binary.LittleEndian, p.Length)
	_ = binary.Write(buf, binary.LittleEndian, p.Check)
	_ = binary.Write(buf, binary.LittleEndian, p.Magic)

	// 写入数据
	if p.Length > 0 {
		buf.Write(p.Data)
	}

	return buf.Bytes()
}

// ParsePacket 从字节流解析数据包头部
func ParsePacket(header []byte) (*Packet, error) {
	if len(header) < 24 {
		return nil, fmt.Errorf("header too short: %d bytes", len(header))
	}

	packet := &Packet{
		Command: Command(binary.LittleEndian.Uint32(header[0:4])),
		Arg0:    binary.LittleEndian.Uint32(header[4:8]),
		Arg1:    binary.LittleEndian.Uint32(header[8:12]),
		Length:  binary.LittleEndian.Uint32(header[12:16]),
		Check:   binary.LittleEndian.Uint32(header[16:20]),
		Magic:   binary.LittleEndian.Uint32(header[20:24]),
	}

	return packet, nil
}

// SetData 设置数据包的数据内容
func (p *Packet) SetData(data []byte) {
	p.Data = data
	if data != nil {
		p.Length = uint32(len(data))
	} else {
		p.Length = 0
	}
}

// IsValid 检查数据包是否有效
func (p *Packet) IsValid() bool {
	return p.VerifyMagic() && p.VerifyChecksum()
}

// Clone 克隆数据包
func (p *Packet) Clone() *Packet {
	clone := &Packet{
		Command: p.Command,
		Arg0:    p.Arg0,
		Arg1:    p.Arg1,
		Length:  p.Length,
		Check:   p.Check,
		Magic:   p.Magic,
	}

	if p.Data != nil {
		clone.Data = make([]byte, len(p.Data))
		copy(clone.Data, p.Data)
	}

	return clone
}

// Equal 比较两个数据包是否相等
func (p *Packet) Equal(other *Packet) bool {
	if other == nil {
		return false
	}

	return p.Command == other.Command &&
		p.Arg0 == other.Arg0 &&
		p.Arg1 == other.Arg1 &&
		p.Length == other.Length &&
		p.Check == other.Check &&
		p.Magic == other.Magic &&
		bytes.Equal(p.Data, other.Data)
}
