package proxy

import (
	"sync"
	"testing"
)

func TestNewRollingCounter(t *testing.T) {
	tests := []struct {
		name        string
		max         uint32
		min         []uint32
		expectedMin uint32
		expectedMax uint32
		expectedNow uint32
	}{
		{
			name:        "default min value",
			max:         100,
			min:         nil,
			expectedMin: 1,
			expectedMax: 100,
			expectedNow: 1,
		},
		{
			name:        "custom min value",
			max:         100,
			min:         []uint32{10},
			expectedMin: 10,
			expectedMax: 100,
			expectedNow: 10,
		},
		{
			name:        "max value",
			max:         UINT32Max,
			min:         nil,
			expectedMin: 1,
			expectedMax: UINT32Max,
			expectedNow: 1,
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				var rc *RollingCounter
				if tt.min != nil {
					rc = NewRollingCounter(tt.max, tt.min[0])
				} else {
					rc = NewRollingCounter(tt.max)
				}

				min, max := rc.GetRange()
				if min != tt.expectedMin {
					t.Errorf("min = %v, want %v", min, tt.expectedMin)
				}
				if max != tt.expectedMax {
					t.Errorf("max = %v, want %v", max, tt.expectedMax)
				}
				if rc.Current() != tt.expectedNow {
					t.Errorf("current = %v, want %v", rc.Current(), tt.expectedNow)
				}
			},
		)
	}
}

func TestRollingCounter_Next(t *testing.T) {
	tests := []struct {
		name     string
		max      uint32
		min      uint32
		calls    int
		expected []uint32
	}{
		{
			name:     "normal increment",
			max:      5,
			min:      1,
			calls:    3,
			expected: []uint32{2, 3, 4},
		},
		{
			name:     "rollover",
			max:      3,
			min:      1,
			calls:    5,
			expected: []uint32{2, 3, 1, 2, 3},
		},
		{
			name:     "single value range",
			max:      1,
			min:      1,
			calls:    3,
			expected: []uint32{1, 1, 1},
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				rc := NewRollingCounter(tt.max, tt.min)

				for i, expectedValue := range tt.expected {
					result := rc.Next()
					if result != expectedValue {
						t.Errorf("call %d: Next() = %v, want %v", i+1, result, expectedValue)
					}
				}
			},
		)
	}
}

func TestRollingCounter_Current(t *testing.T) {
	rc := NewRollingCounter(10, 5)

	// 初始值应该是最小值
	if current := rc.Current(); current != 5 {
		t.Errorf("initial Current() = %v, want %v", current, 5)
	}

	// 调用 Next() 后值应该改变
	next := rc.Next()
	if current := rc.Current(); current != next {
		t.Errorf("Current() = %v, want %v", current, next)
	}
}

func TestRollingCounter_Reset(t *testing.T) {
	rc := NewRollingCounter(10, 3)

	// 增加几次
	rc.Next()
	rc.Next()
	rc.Next()

	// 重置
	rc.Reset()

	if current := rc.Current(); current != 3 {
		t.Errorf("after Reset(), Current() = %v, want %v", current, 3)
	}
}

func TestRollingCounter_SetCurrent(t *testing.T) {
	tests := []struct {
		name     string
		max      uint32
		min      uint32
		setValue uint32
		expected uint32
	}{
		{
			name:     "valid value",
			max:      10,
			min:      1,
			setValue: 5,
			expected: 5,
		},
		{
			name:     "value below min",
			max:      10,
			min:      3,
			setValue: 1,
			expected: 3,
		},
		{
			name:     "value above max",
			max:      10,
			min:      1,
			setValue: 15,
			expected: 10,
		},
		{
			name:     "value at min",
			max:      10,
			min:      2,
			setValue: 2,
			expected: 2,
		},
		{
			name:     "value at max",
			max:      10,
			min:      1,
			setValue: 10,
			expected: 10,
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				rc := NewRollingCounter(tt.max, tt.min)
				rc.SetCurrent(tt.setValue)

				if current := rc.Current(); current != tt.expected {
					t.Errorf("SetCurrent(%v), Current() = %v, want %v", tt.setValue, current, tt.expected)
				}
			},
		)
	}
}

func TestRollingCounter_IsAtMax(t *testing.T) {
	rc := NewRollingCounter(5, 1)

	// 初始状态不应该在最大值
	if rc.IsAtMax() {
		t.Error("initial IsAtMax() = true, want false")
	}

	// 设置到最大值
	rc.SetCurrent(5)
	if !rc.IsAtMax() {
		t.Error("after setting to max, IsAtMax() = false, want true")
	}

	// 超过最大值也应该返回 true
	rc.SetCurrent(10)
	if !rc.IsAtMax() {
		t.Error("after setting above max, IsAtMax() = false, want true")
	}
}

func TestRollingCounter_IsAtMin(t *testing.T) {
	rc := NewRollingCounter(10, 3)

	// 初始状态应该在最小值
	if !rc.IsAtMin() {
		t.Error("initial IsAtMin() = false, want true")
	}

	// 增加后不应该在最小值
	rc.Next()
	if rc.IsAtMin() {
		t.Error("after Next(), IsAtMin() = true, want false")
	}

	// 重置后应该在最小值
	rc.Reset()
	if !rc.IsAtMin() {
		t.Error("after Reset(), IsAtMin() = false, want true")
	}
}

func TestRollingCounter_Remaining(t *testing.T) {
	tests := []struct {
		name     string
		max      uint32
		min      uint32
		current  uint32
		expected uint32
	}{
		{
			name:     "at min",
			max:      10,
			min:      1,
			current:  1,
			expected: 9,
		},
		{
			name:     "in middle",
			max:      10,
			min:      1,
			current:  5,
			expected: 5,
		},
		{
			name:     "at max",
			max:      10,
			min:      1,
			current:  10,
			expected: 0,
		},
		{
			name:     "above max",
			max:      10,
			min:      1,
			current:  15,
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				rc := NewRollingCounter(tt.max, tt.min)
				rc.SetCurrent(tt.current)

				if remaining := rc.Remaining(); remaining != tt.expected {
					t.Errorf("Remaining() = %v, want %v", remaining, tt.expected)
				}
			},
		)
	}
}

func TestRollingCounter_Concurrency(t *testing.T) {
	rc := NewRollingCounter(UINT32Max, 1)
	const numGoroutines = 10
	const numCalls = 100

	var wg sync.WaitGroup
	results := make([][]uint32, numGoroutines)

	// 启动多个 goroutine 并发调用 Next()
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			results[index] = make([]uint32, numCalls)
			for j := 0; j < numCalls; j++ {
				results[index][j] = rc.Next()
			}
		}(i)
	}

	wg.Wait()

	// 收集所有结果
	allResults := make(map[uint32]int)
	for _, goroutineResults := range results {
		for _, value := range goroutineResults {
			allResults[value]++
		}
	}

	// 验证没有数据竞争（所有值都应该是唯一的或接近唯一的）
	totalCalls := numGoroutines * numCalls
	if len(allResults) < totalCalls/2 {
		t.Errorf("too many duplicate values, got %d unique values out of %d calls", len(allResults), totalCalls)
	}

	// 验证所有值都在有效范围内
	for value := range allResults {
		if value < 1 || value > UINT32Max {
			t.Errorf("value %d is out of range [1, %d]", value, UINT32Max)
		}
	}
}
