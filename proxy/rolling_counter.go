package proxy

import (
	"sync"
)

// RollingCounter 滚动计数器，用于生成递增的 ID
// 当达到最大值时会回滚到最小值
type RollingCounter struct {
	mu  sync.Mutex
	now uint32
	max uint32
	min uint32
}

// NewRollingCounter 创建新的滚动计数器
// max: 最大值
// min: 最小值，默认为 1
func NewRollingCounter(max_ uint32, min_ ...uint32) *RollingCounter {
	minVal := uint32(1)
	if len(min_) > 0 {
		minVal = min_[0]
	}

	return &RollingCounter{
		now: minVal,
		max: max_,
		min: minVal,
	}
}

// Next 获取下一个值
func (rc *RollingCounter) Next() uint32 {
	rc.mu.Lock()
	defer rc.mu.Unlock()

	// 如果当前值已经达到最大值，回滚到最小值
	if rc.now >= rc.max {
		rc.now = rc.min
	} else {
		rc.now++
	}

	return rc.now
}

// Current 获取当前值（不递增）
func (rc *RollingCounter) Current() uint32 {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	return rc.now
}

// Reset 重置计数器到最小值
func (rc *RollingCounter) Reset() {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	rc.now = rc.min
}

// SetCurrent 设置当前值
func (rc *RollingCounter) SetCurrent(value uint32) {
	rc.mu.Lock()
	defer rc.mu.Unlock()

	// 确保值在有效范围内
	if value < rc.min {
		rc.now = rc.min
	} else if value > rc.max {
		rc.now = rc.max
	} else {
		rc.now = value
	}
}

// GetRange 获取计数器的范围
func (rc *RollingCounter) GetRange() (min_, max_ uint32) {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	return rc.min, rc.max
}

// IsAtMax 检查是否已达到最大值
func (rc *RollingCounter) IsAtMax() bool {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	return rc.now >= rc.max
}

// IsAtMin 检查是否在最小值
func (rc *RollingCounter) IsAtMin() bool {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	return rc.now == rc.min
}

// Remaining 获取到最大值还剩多少
func (rc *RollingCounter) Remaining() uint32 {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	if rc.now >= rc.max {
		return 0
	}
	return rc.max - rc.now
}
