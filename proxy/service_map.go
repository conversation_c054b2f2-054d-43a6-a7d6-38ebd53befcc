package proxy

import (
	"fmt"
	"sync"
)

type (
	// ServiceMap 服务映射表，用于管理多个服务连接
	ServiceMap struct {
		mu       sync.RWMutex
		services map[uint32]*Service
		count    int
	}
	ServiceMapStats struct {
		TotalCount  int
		ActiveCount int
		ClosedCount int
	}
)

// NewServiceMap 创建新的服务映射表
func NewServiceMap() *ServiceMap {
	return &ServiceMap{
		services: make(map[uint32]*Service),
		count:    0,
	}
}

// Insert 插入新的服务
func (sm *ServiceMap) Insert(remoteID uint32, service *Service) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if _, exists := sm.services[remoteID]; exists {
		return fmt.Errorf("remote ID %d is already being used", remoteID)
	}

	sm.services[remoteID] = service
	sm.count++
	return nil
}

// Get 获取指定`ID`的服务
func (sm *ServiceMap) Get(remoteID uint32) *Service {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	return sm.services[remoteID]
}

// Remove 移除指定`ID`的服务
func (sm *ServiceMap) Remove(remoteID uint32) *Service {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	service, exists := sm.services[remoteID]
	if !exists {
		return nil
	}

	delete(sm.services, remoteID)
	sm.count--
	return service
}

// Close 结束所有服务
func (sm *ServiceMap) Close() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	var lastError error

	for remoteID, service := range sm.services {
		if err := service.Close(); err != nil {
			lastError = err
		}
		delete(sm.services, remoteID)
	}

	sm.count = 0
	return lastError
}

// Count 获取当前服务数量
func (sm *ServiceMap) Count() int {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.count
}

// IsEmpty 检查是否为空
func (sm *ServiceMap) IsEmpty() bool {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.count == 0
}

// GetAllIDs 获取所有服务的`ID`
func (sm *ServiceMap) GetAllIDs() []uint32 {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	ids := make([]uint32, 0, sm.count)
	for id := range sm.services {
		ids = append(ids, id)
	}

	return ids
}

// GetAllServices 获取所有服务
func (sm *ServiceMap) GetAllServices() []*Service {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	services := make([]*Service, 0, sm.count)
	for _, service := range sm.services {
		services = append(services, service)
	}

	return services
}

// Exists 检查指定`ID`的服务是否存在
func (sm *ServiceMap) Exists(remoteID uint32) bool {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	_, exists := sm.services[remoteID]
	return exists
}

// Clear 清空所有服务（不调用 Close）
func (sm *ServiceMap) Clear() {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.services = make(map[uint32]*Service)
	sm.count = 0
}

// ForEach 遍历所有服务
func (sm *ServiceMap) ForEach(fn func(uint32, *Service) bool) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	for id, service := range sm.services {
		if !fn(id, service) {
			break
		}
	}
}

// RemoveClosed 移除所有已关闭的服务
func (sm *ServiceMap) RemoveClosed() int {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	removed := 0
	for id, service := range sm.services {
		if service.IsClosed() {
			delete(sm.services, id)
			sm.count--
			removed++
		}
	}

	return removed
}

// GetStats 获取统计信息
func (sm *ServiceMap) GetStats() ServiceMapStats {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	stats := ServiceMapStats{
		TotalCount:  sm.count,
		ActiveCount: 0,
		ClosedCount: 0,
	}

	for _, service := range sm.services {
		if service.IsClosed() {
			stats.ClosedCount++
		} else {
			stats.ActiveCount++
		}
	}

	return stats
}
