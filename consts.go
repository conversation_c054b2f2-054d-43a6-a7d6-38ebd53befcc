package gadb

import "os"

const (
	TAG = "[DEBUG] [gdb]"

	readChunkSize    = 1024
	syncMaxChunkSize = 64 * 1024

	DefaultFileMode = os.FileMode(0o664)
	DefaultDirMode  = os.FileMode(0o755)
)

type protocol string

const (
	OKAY protocol = "OKAY"
	FAIL protocol = "FAIL"
	STAT protocol = "STAT"
	LIST protocol = "LIST"
	DENT protocol = "DENT"
	RECV protocol = "RECV"
	DATA protocol = "DATA"
	DONE protocol = "DONE"
	SEND protocol = "SEND"
	QUIT protocol = "QUIT"
)

const (
	adbCmdStartServer = "start-server"

	hostServicePrefix         = "host:"
	hostServiceOfSerialPrefix = "host-serial:"

	hostServiceOfVersion                 = hostServicePrefix + "version"
	hostServiceOfDevices                 = hostServicePrefix + "devices"
	hostServiceOfDevicesL                = hostServicePrefix + "devices-l"
	hostServiceOfListForward             = hostServicePrefix + "list-forward"
	hostServiceOfKillForwardAll          = hostServicePrefix + "killforward-all"
	hostServiceOfConnectWithIPAndPort    = hostServicePrefix + "connect:%s:%d"
	hostServiceOfDisconnect              = hostServicePrefix + "disconnect:"
	hostServiceOfDisconnectByIP          = hostServiceOfDisconnect + "%s"
	hostServiceOfDisconnectByIPAndPort   = hostServiceOfDisconnect + "%s:%d"
	hostServiceOfKill                    = hostServicePrefix + "kill"
	hostServiceOfGetStateBySerial        = hostServiceOfSerialPrefix + "%s:get-state"
	hostServiceOfGetDevPathBySerial      = hostServiceOfSerialPrefix + "%s:get-devpath"
	hostServiceOfForwardNoRebindBySerial = hostServiceOfSerialPrefix + "%s:forward:norebind:%s;%s"
	hostServiceOfForwardBySerial         = hostServiceOfSerialPrefix + "%s:forward:%s;%s"
	hostServiceOfKillForwardBySerial     = hostServiceOfSerialPrefix + "%s:killforward:%s"
	hostServiceOfTransportBySerial       = hostServicePrefix + "transport:%s"

	localServiceOfShell = "shell:%s"
	localServiceOfTCPIP = "tcpip:%d"
	localServiceOfSync  = "sync:"
)

type DeviceState string

const (
	DeviceStateOfUnknown      DeviceState = "UNKNOWN"
	DeviceStateOfOnline       DeviceState = "online"
	DeviceStateOfOffline      DeviceState = "offline"
	DeviceStateOfDisconnected DeviceState = "disconnected"
)

type ShellMessageType byte

const (
	shellMessageTypeOfStdin      ShellMessageType = 0
	shellMessageTypeOfStdout     ShellMessageType = 1
	shellMessageTypeOfStderr     ShellMessageType = 2
	shellMessageTypeOfExit       ShellMessageType = 3
	shellMessageTypeOfCloseStdin ShellMessageType = 4
	shellMessageTypeOfInvalid    ShellMessageType = 255
)
