package gadb

import (
	"bufio"
	"errors"
	"fmt"
	"strings"
)

const (
	attrKeyValSeparator = ":"

	attrKeyOfProduct     = "product"
	attrKeyOfModel       = "model"
	attrKeyOfDevice      = "device"
	attrKeyOfTransportID = "transport_id"
	attrKeyOfUSB         = "usb"
)

var attrKeys = map[string]struct{}{
	attrKeyOfProduct:     {},
	attrKeyOfModel:       {},
	attrKeyOfDevice:      {},
	attrKeyOfTransportID: {},
	attrKeyOfUSB:         {},
}

type DeviceInfo struct {
	// always set.
	Serial string `json:"serial"`

	// product, model, device and transport_id are not set in the short form.
	Product     string `json:"product"`
	Model       string `json:"model"`
	Device      string `json:"device"`
	TransportID string `json:"transport_id"`

	// only set for devices connected via USB.
	USB string `json:"usb"`
}

// IsUsb returns true if the device is connected via USB.
func (d *DeviceInfo) IsUsb() bool {
	return d.USB != ""
}

func newDeviceInfo(serial string, attrs map[string]string) (*DeviceInfo, error) {
	if serial == "" {
		return nil, errors.New("device serial cannot be blank")
	}

	return &DeviceInfo{
		Serial:      serial,
		Product:     attrs[attrKeyOfProduct],
		Model:       attrs[attrKeyOfModel],
		Device:      attrs[attrKeyOfDevice],
		TransportID: attrs[attrKeyOfTransportID],
		USB:         attrs[attrKeyOfUSB],
	}, nil
}

func parseDeviceList(list string, lineParseFunc func(string) (*DeviceInfo, error)) ([]*DeviceInfo, error) {
	var devices []*DeviceInfo
	scanner := bufio.NewScanner(strings.NewReader(list))

	for scanner.Scan() {
		device, err := lineParseFunc(scanner.Text())
		if err != nil {
			return nil, err
		}
		devices = append(devices, device)
	}

	return devices, nil
}

func parseDeviceShort(line string) (*DeviceInfo, error) {
	fields := strings.Fields(line)
	if len(fields) != 2 {
		return nil, fmt.Errorf(
			"malformed device line: %q, expected 2 fields, but got %d", line, len(fields),
		)
	}

	return newDeviceInfo(fields[0], map[string]string{})
}

func parseDeviceLong(line string) (*DeviceInfo, error) {
	fields := strings.Fields(line)
	if len(fields) <= 2 {
		return nil, fmt.Errorf(
			"malformed device line: %q, expected greater than 2 fields, but got %d", line, len(fields),
		)
	}

	attrs := parseDeviceAttributes(fields[2:])
	return newDeviceInfo(fields[0], attrs)
}

func parseDeviceAttributes(fields []string) map[string]string {
	attrs := make(map[string]string, len(fields))
	for _, field := range fields {
		key, val := parseKeyVal(field)
		if key == "" {
			continue
		} else if _, ok := attrKeys[key]; !ok {
			continue
		}

		attrs[key] = val
	}

	return attrs
}

// Parses a key:val pair and returns key, val.
func parseKeyVal(pair string) (string, string) {
	split := strings.Split(pair, attrKeyValSeparator)

	switch len(split) {
	case 0:
		return "", ""
	case 1:
		return split[0], ""
	default:
		return split[0], split[1]
	}
}
