package gadb

import (
	"io/fs"
	"path/filepath"
	"syscall"
	"time"
)

var _ fs.FileInfo = (*FileInfo)(nil)

type FileInfo struct {
	name    string
	mode    uint32
	size    uint32
	modTime time.Time
}

func (info *FileInfo) Path() string {
	return info.name
}

func (info *FileInfo) Name() string {
	return filepath.Base(info.name)
}

func (info *FileInfo) Size() int64 {
	return int64(info.size)
}

func (info *FileInfo) Mode() fs.FileMode {
	fm := fs.FileMode(info.mode & 0o777)
	switch info.mode & syscall.S_IFMT {
	case syscall.S_IFBLK:
		fm |= fs.ModeDevice
	case syscall.S_IFCHR:
		fm |= fs.ModeDevice | fs.ModeCharDevice
	case syscall.S_IFDIR:
		fm |= fs.ModeDir
	case syscall.S_IFIFO:
		fm |= fs.ModeNamedPipe
	case syscall.S_IFLNK:
		fm |= fs.ModeSymlink
	case syscall.S_IFREG:
		// nothing to do
	case syscall.S_IFSOCK:
		fm |= fs.ModeSocket
	}

	if (info.mode & syscall.S_ISGID) != 0 {
		fm |= fs.ModeSetgid
	}
	if (info.mode & syscall.S_ISUID) != 0 {
		fm |= fs.ModeSetuid
	}
	if (info.mode & syscall.S_ISVTX) != 0 {
		fm |= fs.ModeSticky
	}

	return fm
}

func (info *FileInfo) ModTime() time.Time {
	return info.modTime
}

func (info *FileInfo) IsDir() bool {
	return (info.mode & syscall.S_IFDIR) != 0
}

func (info *FileInfo) IsFile() bool {
	return (info.mode & syscall.S_IFREG) != 0
}

func (info *FileInfo) Sys() any {
	return nil
}
